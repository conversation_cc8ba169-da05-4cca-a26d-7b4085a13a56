// Main initialization script for Windows XP File Manager

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the file manager
    const fileManager = new FileManager();
    
    // Make it globally available for debugging
    window.fileManager = fileManager;
    
    // Initialize window controls
    initializeWindowControls();
    
    // Set up any additional event listeners
    setupGlobalEventListeners();
    
    console.log('Windows XP File Manager initialized successfully');
});

/**
 * Initialize window control buttons (minimize, maximize, close)
 */
function initializeWindowControls() {
    const minimizeBtn = document.querySelector('.minimize-btn');
    const maximizeBtn = document.querySelector('.maximize-btn');
    const closeBtn = document.querySelector('.close-btn');
    
    if (minimizeBtn) {
        minimizeBtn.addEventListener('click', () => {
            // In a real application, this would minimize the window
            console.log('Minimize clicked');
        });
    }
    
    if (maximizeBtn) {
        maximizeBtn.addEventListener('click', () => {
            // Toggle fullscreen or maximize state
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                document.documentElement.requestFullscreen();
            }
        });
    }
    
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            // In a real application, this would close the window
            if (confirm('Are you sure you want to close the File Manager?')) {
                window.close();
            }
        });
    }
}

/**
 * Initialize sidebar functionality (simplified)
 */
function initializeSidebar() {
    // Sidebar is now handled by the FileManager class
    console.log('Sidebar initialized with drives');
}

/**
 * Set up global event listeners
 */
function setupGlobalEventListeners() {
    // Handle window resize
    window.addEventListener('resize', () => {
        updateResponsiveLayout();
    });
    
    // Handle menu bar clicks
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', () => {
            handleMenuClick(item.textContent);
        });
    });
    
    // Prevent default drag and drop
    document.addEventListener('dragover', (e) => {
        e.preventDefault();
    });
    
    document.addEventListener('drop', (e) => {
        e.preventDefault();
    });
}

/**
 * Show system information dialog
 */
function showSystemInfo() {
    alert('System Information\n\nWindows XP Professional\nVersion 2002\nService Pack 3\n\nComputer: DESKTOP-PC\nProcessor: Intel Pentium 4\nMemory: 1.00 GB RAM');
}

/**
 * Show Add/Remove Programs dialog
 */
function showAddRemovePrograms() {
    alert('Add or Remove Programs\n\nThis would open the Add or Remove Programs control panel in a real Windows XP system.');
}

/**
 * Show Control Panel
 */
function showControlPanel() {
    alert('Control Panel\n\nThis would open the Control Panel in a real Windows XP system.');
}

/**
 * Navigate to My Documents (kept for potential future use)
 */
function navigateToMyDocuments() {
    if (window.fileManager) {
        window.fileManager.navigate(['My Computer', 'C:', 'Documents and Settings', 'Administrator', 'My Documents']);
    }
}

/**
 * Handle menu bar clicks
 * @param {string} menuText - Text of the clicked menu item
 */
function handleMenuClick(menuText) {
    switch (menuText) {
        case 'File':
            showFileMenu();
            break;
        case 'Edit':
            showEditMenu();
            break;
        case 'View':
            showViewMenu();
            break;
        case 'Tools':
            showToolsMenu();
            break;
        case 'Help':
            showHelpMenu();
            break;
    }
}

/**
 * Show File menu
 */
function showFileMenu() {
    console.log('File menu clicked');
    // In a real implementation, this would show a dropdown menu
}

/**
 * Show Edit menu
 */
function showEditMenu() {
    console.log('Edit menu clicked');
    // In a real implementation, this would show a dropdown menu
}

/**
 * Show View menu
 */
function showViewMenu() {
    console.log('View menu clicked');
    // In a real implementation, this would show a dropdown menu
}

/**
 * Show Tools menu
 */
function showToolsMenu() {
    console.log('Tools menu clicked');
    // In a real implementation, this would show a dropdown menu
}

/**
 * Show Help menu
 */
function showHelpMenu() {
    alert('Windows XP File Manager\n\nVersion 1.0\n\nA recreation of the classic Windows XP file manager interface.\n\nFeatures:\n- Browse drives and folders\n- View files in different layouts\n- File details panel\n- Windows XP authentic styling');
}

/**
 * Update responsive layout
 */
function updateResponsiveLayout() {
    const sidebar = document.querySelector('.sidebar');
    const detailsPanel = document.querySelector('.details-panel');
    const windowWidth = window.innerWidth;
    
    // Hide sidebar and details panel on small screens
    if (windowWidth < 768) {
        if (sidebar) sidebar.style.display = 'none';
        if (detailsPanel) detailsPanel.style.display = 'none';
    } else {
        if (sidebar) sidebar.style.display = 'block';
        if (detailsPanel) detailsPanel.style.display = 'flex';
    }
}

/**
 * Create placeholder icons if they don't exist
 */
function createPlaceholderIcons() {
    // This function would create placeholder icons for missing assets
    // For now, we'll just log that icons might be missing
    console.log('Note: Some icons may not be available. In a real implementation, you would include actual icon files.');
}

// Initialize placeholder icons
createPlaceholderIcons();
