// Date Utility Functions for Windows XP File Manager

const DateUtils = {
    /**
     * Format date in Windows XP style
     * @param {Date} date - The date to format
     * @returns {string} Formatted date string
     */
    formatXPDate(date) {
        if (!date || !(date instanceof Date)) {
            return 'Unknown';
        }
        
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();
        
        let hours = date.getHours();
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';
        
        hours = hours % 12;
        hours = hours ? hours : 12; // 0 should be 12
        hours = String(hours).padStart(2, '0');
        
        return `${month}/${day}/${year} ${hours}:${minutes} ${ampm}`;
    },
    
    /**
     * Format date for details panel
     * @param {Date} date - The date to format
     * @returns {string} Formatted date string
     */
    formatDetailDate(date) {
        if (!date || !(date instanceof Date)) {
            return 'Unknown';
        }
        
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        };
        
        return date.toLocaleDateString('en-US', options);
    },
    
    /**
     * Get relative time string (e.g., "2 hours ago")
     * @param {Date} date - The date to compare
     * @returns {string} Relative time string
     */
    getRelativeTime(date) {
        if (!date || !(date instanceof Date)) {
            return 'Unknown';
        }
        
        const now = new Date();
        const diffMs = now - date;
        const diffSecs = Math.floor(diffMs / 1000);
        const diffMins = Math.floor(diffSecs / 60);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        const diffWeeks = Math.floor(diffDays / 7);
        const diffMonths = Math.floor(diffDays / 30);
        const diffYears = Math.floor(diffDays / 365);
        
        if (diffSecs < 60) {
            return 'Just now';
        } else if (diffMins < 60) {
            return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
        } else if (diffHours < 24) {
            return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        } else if (diffDays < 7) {
            return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
        } else if (diffWeeks < 4) {
            return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
        } else if (diffMonths < 12) {
            return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
        } else {
            return `${diffYears} year${diffYears !== 1 ? 's' : ''} ago`;
        }
    },
    
    /**
     * Check if date is today
     * @param {Date} date - The date to check
     * @returns {boolean} True if date is today
     */
    isToday(date) {
        if (!date || !(date instanceof Date)) {
            return false;
        }
        
        const today = new Date();
        return date.toDateString() === today.toDateString();
    },
    
    /**
     * Check if date is yesterday
     * @param {Date} date - The date to check
     * @returns {boolean} True if date is yesterday
     */
    isYesterday(date) {
        if (!date || !(date instanceof Date)) {
            return false;
        }
        
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return date.toDateString() === yesterday.toDateString();
    },
    
    /**
     * Check if date is this week
     * @param {Date} date - The date to check
     * @returns {boolean} True if date is this week
     */
    isThisWeek(date) {
        if (!date || !(date instanceof Date)) {
            return false;
        }
        
        const now = new Date();
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        return date >= weekStart && date <= weekEnd;
    },
    
    /**
     * Sort dates in ascending order
     * @param {Date} a - First date
     * @param {Date} b - Second date
     * @returns {number} Sort comparison result
     */
    sortAscending(a, b) {
        if (!a || !(a instanceof Date)) return 1;
        if (!b || !(b instanceof Date)) return -1;
        return a - b;
    },
    
    /**
     * Sort dates in descending order
     * @param {Date} a - First date
     * @param {Date} b - Second date
     * @returns {number} Sort comparison result
     */
    sortDescending(a, b) {
        if (!a || !(a instanceof Date)) return 1;
        if (!b || !(b instanceof Date)) return -1;
        return b - a;
    },
    
    /**
     * Create a date from Windows XP era (around 2010)
     * @param {number} year - Year (default: random between 2009-2011)
     * @param {number} month - Month (default: random 1-12)
     * @param {number} day - Day (default: random valid day)
     * @param {number} hour - Hour (default: random 0-23)
     * @param {number} minute - Minute (default: random 0-59)
     * @returns {Date} Generated date
     */
    createXPEraDate(year, month, day, hour, minute) {
        const y = year || (2009 + Math.floor(Math.random() * 3)); // 2009-2011
        const m = month || (1 + Math.floor(Math.random() * 12)); // 1-12
        const d = day || (1 + Math.floor(Math.random() * 28)); // 1-28 (safe for all months)
        const h = hour !== undefined ? hour : Math.floor(Math.random() * 24); // 0-23
        const min = minute !== undefined ? minute : Math.floor(Math.random() * 60); // 0-59
        
        return new Date(y, m - 1, d, h, min, 0, 0);
    }
};

// Make DateUtils globally available
window.DateUtils = DateUtils;
