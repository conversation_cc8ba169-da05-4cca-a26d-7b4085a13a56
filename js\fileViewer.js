// File Viewer Component for Windows XP File Manager

class FileViewer {
    constructor() {
        this.overlay = document.getElementById('popup-overlay');
        this.window = this.overlay.querySelector('.popup-window');
        this.titleElement = document.getElementById('popup-title');
        this.contentElement = document.getElementById('popup-content');
        this.closeButton = document.getElementById('popup-close');
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // Close button
        this.closeButton.addEventListener('click', () => {
            this.close();
        });
        
        // Close on overlay click
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close();
            }
        });
        
        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen()) {
                this.close();
            }
        });
    }
    
    /**
     * Open file in viewer
     * @param {string} filename - Name of the file
     * @param {Object} fileData - File data object
     */
    openFile(filename, fileData) {
        this.titleElement.textContent = filename;
        this.contentElement.innerHTML = '';
        
        // Create content based on file type
        const content = this.createFileContent(filename, fileData);
        this.contentElement.appendChild(content);
        
        // Show the popup
        this.overlay.style.display = 'flex';
        
        // Focus the close button for accessibility
        this.closeButton.focus();
    }
    
    /**
     * Create content for different file types
     * @param {string} filename - Name of the file
     * @param {Object} fileData - File data object
     * @returns {HTMLElement} Content element
     */
    createFileContent(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'file-viewer-content';
        
        const extension = FileUtils.getFileExtension(filename);
        
        if (FileUtils.isImage(extension)) {
            return this.createImageViewer(filename, fileData);
        } else if (FileUtils.isDocument(extension)) {
            return this.createDocumentViewer(filename, fileData);
        } else if (FileUtils.isSpreadsheet(extension)) {
            return this.createSpreadsheetViewer(filename, fileData);
        } else if (FileUtils.isAudio(extension)) {
            return this.createAudioViewer(filename, fileData);
        } else if (FileUtils.isVideo(extension)) {
            return this.createVideoViewer(filename, fileData);
        } else if (extension === 'pdf') {
            return this.createPDFViewer(filename, fileData);
        } else {
            return this.createGenericViewer(filename, fileData);
        }
    }
    
    /**
     * Create image viewer
     */
    createImageViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'image-viewer';
        container.style.textAlign = 'center';
        
        const img = document.createElement('img');
        img.src = `assets/sample-files/images/${filename}`;
        img.alt = filename;
        img.style.maxWidth = '100%';
        img.style.maxHeight = '400px';
        img.style.border = '1px solid #ccc';
        
        // Fallback if image doesn't exist
        img.onerror = () => {
            img.style.display = 'none';
            const placeholder = document.createElement('div');
            placeholder.style.cssText = `
                width: 200px;
                height: 150px;
                background: #f0f0f0;
                border: 2px dashed #ccc;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 20px auto;
                font-family: Tahoma, sans-serif;
                font-size: 11px;
                color: #666;
            `;
            placeholder.textContent = 'Image Preview Not Available';
            container.appendChild(placeholder);
        };
        
        container.appendChild(img);
        
        // Add image info
        const info = document.createElement('div');
        info.style.marginTop = '10px';
        info.innerHTML = `
            <p><strong>File:</strong> ${filename}</p>
            <p><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
            <p><strong>Type:</strong> ${FileUtils.getFileTypeDescription(extension)}</p>
            <p><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
        `;
        container.appendChild(info);
        
        return container;
    }
    
    /**
     * Create document viewer
     */
    createDocumentViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'document-viewer';
        
        // Create icon container for SVG support
        const iconContainer = document.createElement('div');
        iconContainer.style.width = '48px';
        iconContainer.style.height = '48px';
        iconContainer.style.display = 'block';
        iconContainer.style.margin = '0 auto 20px';

        const iconData = FileUtils.getItemIcon(fileData);
        if (iconData.startsWith('<svg')) {
            iconContainer.innerHTML = iconData;
            iconContainer.querySelector('svg').style.width = '48px';
            iconContainer.querySelector('svg').style.height = '48px';
        } else {
            const icon = document.createElement('img');
            icon.src = iconData;
            icon.style.width = '48px';
            icon.style.height = '48px';
            iconContainer.appendChild(icon);
        }
        
        container.appendChild(iconContainer);
        
        const content = document.createElement('div');

        // Check if file has actual content
        const documentContent = fileData.content || 'This document does not contain viewable content.';

        content.innerHTML = `
            <h3 style="text-align: center; margin-bottom: 20px;">${filename}</h3>
            <div style="background: #ffffff; padding: 15px; border: 2px inset #ece9d8; margin-bottom: 15px; height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; white-space: pre-wrap;">${documentContent}</div>
            <div style="font-size: 11px; color: #666;">
                <p><strong>File:</strong> ${filename}</p>
                <p><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
                <p><strong>Type:</strong> ${FileUtils.getFileTypeDescription(FileUtils.getFileExtension(filename))}</p>
                <p><strong>Created:</strong> ${DateUtils.formatXPDate(fileData.created)}</p>
                <p><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
            </div>
        `;
        
        container.appendChild(content);
        return container;
    }
    
    /**
     * Create spreadsheet viewer
     */
    createSpreadsheetViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'spreadsheet-viewer';

        // Create icon container for SVG support
        const iconContainer = document.createElement('div');
        iconContainer.style.width = '48px';
        iconContainer.style.height = '48px';
        iconContainer.style.display = 'block';
        iconContainer.style.margin = '0 auto 20px';

        const iconData = FileUtils.getItemIcon(fileData);
        if (iconData.startsWith('<svg')) {
            iconContainer.innerHTML = iconData;
            iconContainer.querySelector('svg').style.width = '48px';
            iconContainer.querySelector('svg').style.height = '48px';
        } else {
            const icon = document.createElement('img');
            icon.src = iconData;
            icon.style.width = '48px';
            icon.style.height = '48px';
            iconContainer.appendChild(icon);
        }

        container.appendChild(iconContainer);

        const content = document.createElement('div');

        // Check if file has actual content
        const spreadsheetContent = fileData.content || 'This spreadsheet does not contain viewable content.';

        content.innerHTML = `
            <h3 style="text-align: center; margin-bottom: 20px;">${filename}</h3>
            <div style="background: #ffffff; padding: 15px; border: 2px inset #ece9d8; margin-bottom: 15px; height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.4; white-space: pre-wrap;">${spreadsheetContent}</div>
            <div style="font-size: 11px; color: #666;">
                <p><strong>File:</strong> ${filename}</p>
                <p><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
                <p><strong>Type:</strong> ${FileUtils.getFileTypeDescription(FileUtils.getFileExtension(filename))}</p>
                <p><strong>Created:</strong> ${DateUtils.formatXPDate(fileData.created)}</p>
                <p><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
            </div>
        `;

        container.appendChild(content);
        return container;
    }

    /**
     * Create audio viewer
     */
    createAudioViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'audio-viewer';
        container.style.textAlign = 'center';
        
        const icon = document.createElement('img');
        icon.src = FileUtils.getItemIcon(fileData);
        icon.style.width = '64px';
        icon.style.height = '64px';
        icon.style.margin = '20px auto';
        icon.style.display = 'block';
        
        container.appendChild(icon);
        
        const info = document.createElement('div');
        info.innerHTML = `
            <h3>${filename}</h3>
            <p style="margin: 20px 0;">🎵 Audio file ready to play</p>
            <div style="background: #f0f0f0; padding: 10px; margin: 20px 0; border: 1px inset #ece9d8;">
                <p style="margin: 5px 0;"><strong>Duration:</strong> 3:45</p>
                <p style="margin: 5px 0;"><strong>Bitrate:</strong> 128 kbps</p>
                <p style="margin: 5px 0;"><strong>Format:</strong> ${FileUtils.getFileTypeDescription(FileUtils.getFileExtension(filename))}</p>
            </div>
            <div style="font-size: 11px; color: #666; text-align: left;">
                <p><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
                <p><strong>Created:</strong> ${DateUtils.formatXPDate(fileData.created)}</p>
                <p><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
            </div>
        `;
        
        container.appendChild(info);
        return container;
    }
    
    /**
     * Create video viewer
     */
    createVideoViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'video-viewer';
        container.style.textAlign = 'center';
        
        const placeholder = document.createElement('div');
        placeholder.style.cssText = `
            width: 320px;
            height: 240px;
            background: #000;
            border: 2px inset #ece9d8;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            color: white;
            font-family: Tahoma, sans-serif;
            font-size: 14px;
        `;
        placeholder.innerHTML = '▶ Video Preview<br><small>Click to play</small>';
        
        container.appendChild(placeholder);
        
        const info = document.createElement('div');
        info.innerHTML = `
            <h3>${filename}</h3>
            <div style="background: #f0f0f0; padding: 10px; margin: 20px 0; border: 1px inset #ece9d8;">
                <p style="margin: 5px 0;"><strong>Duration:</strong> 15:32</p>
                <p style="margin: 5px 0;"><strong>Resolution:</strong> 640x480</p>
                <p style="margin: 5px 0;"><strong>Format:</strong> ${FileUtils.getFileTypeDescription(FileUtils.getFileExtension(filename))}</p>
            </div>
            <div style="font-size: 11px; color: #666; text-align: left;">
                <p><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
                <p><strong>Created:</strong> ${DateUtils.formatXPDate(fileData.created)}</p>
                <p><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
            </div>
        `;
        
        container.appendChild(info);
        return container;
    }
    
    /**
     * Create PDF viewer
     */
    createPDFViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'pdf-viewer';
        
        const header = document.createElement('div');
        header.style.textAlign = 'center';
        header.style.marginBottom = '20px';
        
        // Create icon container for SVG support
        const iconContainer = document.createElement('div');
        iconContainer.style.width = '48px';
        iconContainer.style.height = '48px';
        iconContainer.style.display = 'block';
        iconContainer.style.margin = '0 auto 10px';

        const iconData = FileUtils.getItemIcon(fileData);
        if (iconData.startsWith('<svg')) {
            iconContainer.innerHTML = iconData;
            iconContainer.querySelector('svg').style.width = '48px';
            iconContainer.querySelector('svg').style.height = '48px';
        } else {
            const icon = document.createElement('img');
            icon.src = iconData;
            icon.style.width = '48px';
            icon.style.height = '48px';
            iconContainer.appendChild(icon);
        }

        header.appendChild(iconContainer);
        header.innerHTML += `<h3>${filename}</h3>`;
        container.appendChild(header);
        
        const preview = document.createElement('div');
        preview.style.cssText = `
            background: white;
            border: 2px inset #ece9d8;
            height: 300px;
            margin: 20px 0;
            padding: 20px;
            overflow-y: auto;
            font-family: Times, serif;
            line-height: 1.5;
        `;
        // Check if file has actual content
        const pdfContent = fileData.content || 'This PDF document does not contain viewable content.';

        preview.innerHTML = `<div style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.3;">${pdfContent}</div>`;
        
        container.appendChild(preview);
        
        const info = document.createElement('div');
        info.style.fontSize = '11px';
        info.style.color = '#666';
        info.innerHTML = `
            <p><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
            <p><strong>Pages:</strong> 5</p>
            <p><strong>Created:</strong> ${DateUtils.formatXPDate(fileData.created)}</p>
            <p><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
        `;
        
        container.appendChild(info);
        return container;
    }
    
    /**
     * Create generic file viewer
     */
    createGenericViewer(filename, fileData) {
        const container = document.createElement('div');
        container.className = 'generic-viewer';
        container.style.textAlign = 'center';
        
        const icon = document.createElement('img');
        icon.src = FileUtils.getItemIcon(fileData);
        icon.style.width = '64px';
        icon.style.height = '64px';
        icon.style.margin = '20px auto';
        icon.style.display = 'block';
        
        container.appendChild(icon);
        
        const info = document.createElement('div');
        info.innerHTML = `
            <h3>${filename}</h3>
            <p style="margin: 20px 0; color: #666;">This file type cannot be previewed.</p>
            <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border: 1px inset #ece9d8; text-align: left;">
                <p style="margin: 5px 0;"><strong>File:</strong> ${filename}</p>
                <p style="margin: 5px 0;"><strong>Size:</strong> ${FileUtils.formatFileSize(fileData.size)}</p>
                <p style="margin: 5px 0;"><strong>Type:</strong> ${FileUtils.getFileTypeDescription(FileUtils.getFileExtension(filename))}</p>
                <p style="margin: 5px 0;"><strong>Created:</strong> ${DateUtils.formatXPDate(fileData.created)}</p>
                <p style="margin: 5px 0;"><strong>Modified:</strong> ${DateUtils.formatXPDate(fileData.modified)}</p>
            </div>
        `;
        
        container.appendChild(info);
        return container;
    }
    
    /**
     * Close the file viewer
     */
    close() {
        this.overlay.style.display = 'none';
        this.contentElement.innerHTML = '';
    }
    
    /**
     * Check if viewer is open
     * @returns {boolean} True if viewer is open
     */
    isOpen() {
        return this.overlay.style.display !== 'none';
    }
}

// Make FileViewer globally available
window.FileViewer = FileViewer;
