// Mock File System Data for Windows XP File Manager

const fileSystem = {
    drives: {
        'C:': {
            name: 'Local Disk (C:)',
            type: 'hard-drive',
            totalSpace: 80000000000, // 80 GB
            usedSpace: 45000000000,  // 45 GB
            fileSystem: 'NTFS',
            contents: {
                // Documents moved to root level
                'resume.doc': {
                    type: 'document',
                    size: 28672,
                    created: new Date('2010-02-10T11:45:00'),
                    modified: new Date('2010-05-18T13:20:00'),
                    extension: 'doc',
                    content: `RESUME

ARJUN PATIL
Software Engineer
Email: <EMAIL>
Phone: +91-**********
Address: 456, FC Road, Pune, Maharashtra - 411005

OBJECTIVE:
Seeking a challenging position as a Software Engineer in a reputed IT company where I can utilize my technical skills and contribute to innovative projects.

EDUCATION:
• B.Tech in Computer Science Engineering
  Indian Institute of Technology (IIT), Bombay
  CGPA: 8.5/10 (2006-2010)

• Higher Secondary (12th)
  Fergusson College, Pune
  Percentage: 92% (2006)

TECHNICAL SKILLS:
• Programming Languages: Java, C++, Python, JavaScript
• Databases: MySQL, Oracle, MongoDB
• Web Technologies: HTML, CSS, JSP, Servlets
• Operating Systems: Windows, Linux, Unix
• Tools: Eclipse, NetBeans, Git, SVN

WORK EXPERIENCE:
Software Engineer | Infosys Technologies Ltd. | Pune
June 2010 - Present
• Developed web applications using Java and Spring framework
• Worked on database optimization and performance tuning
• Collaborated with cross-functional teams for project delivery
• Mentored junior developers and conducted code reviews

PROJECTS:
1. Online Banking System (2010)
   - Developed secure online banking portal using Java/JSP
   - Implemented transaction processing and account management
   - Technologies: Java, MySQL, Apache Tomcat

2. E-Commerce Website (2009)
   - Built complete e-commerce solution with shopping cart
   - Integrated payment gateway and inventory management
   - Technologies: PHP, MySQL, JavaScript

ACHIEVEMENTS:
• Employee of the Month - March 2010 (Infosys)
• Best Project Award - Final Year Project (IIT Bombay)
• Secured 2nd position in National Programming Contest (2009)

LANGUAGES:
• Marathi (Native)
• Hindi (Fluent)
• English (Fluent)

HOBBIES:
• Cricket, Reading, Photography, Traveling

REFERENCES:
Available upon request.`
                },
                'budget_2010.xls': {
                    type: 'spreadsheet',
                    size: 45056,
                    created: new Date('2010-01-05T08:00:00'),
                    modified: new Date('2010-08-15T17:30:00'),
                    extension: 'xls',
                    content: `FAMILY BUDGET 2010 - PATIL HOUSEHOLD

MONTHLY INCOME:                          Amount (₹)
Salary (Arjun Patil)                     45,000
Freelance Work                           8,000
Investment Returns                       2,500
TOTAL INCOME                            55,500

MONTHLY EXPENSES:
Housing:
  Rent (2BHK Apartment, Pune)           18,000
  Electricity Bill                       1,200
  Water Bill                              400
  Maintenance                             800
  Internet & Cable                        600
  Subtotal                              21,000

Food & Groceries:
  Groceries (Rice, Dal, Vegetables)      4,500
  Milk & Dairy                           1,200
  Eating Out                             2,000
  Subtotal                               7,700

Transportation:
  Petrol (Maruti 800)                    2,500
  Auto/Bus Fare                           800
  Vehicle Maintenance                     500
  Subtotal                               3,800

Utilities & Communication:
  Mobile Bills (2 phones)                 800
  Landline                                300
  Gas Cylinder                            400
  Subtotal                               1,500

Personal & Healthcare:
  Medical/Medicines                      1,500
  Clothing                               2,000
  Personal Care                           800
  Subtotal                               4,300

Education & Entertainment:
  Books & Magazines                       500
  Movies & Entertainment                 1,200
  Gym Membership                          800
  Subtotal                               2,500

Savings & Investments:
  Fixed Deposit                          8,000
  Mutual Funds                           3,000
  Emergency Fund                         2,000
  Subtotal                              13,000

TOTAL EXPENSES                          53,800
NET SAVINGS                              1,700

ANNUAL SUMMARY:
Total Annual Income                    6,66,000
Total Annual Expenses                  6,45,600
Total Annual Savings                     20,400

NOTES:
- Festival expenses (Ganesh Chaturthi, Diwali) budgeted separately
- Planning for new two-wheeler purchase in 2011
- Considering investment in PPF for tax savings
- Emergency fund target: ₹50,000 by year end

Last Updated: August 15, 2010`
                },
                'tax_documents.pdf': {
                    type: 'pdf',
                    size: 156789,
                    created: new Date('2010-04-12T16:00:00'),
                    modified: new Date('2010-04-12T16:00:00'),
                    extension: 'pdf',
                    content: `INCOME TAX RETURN - ASSESSMENT YEAR 2010-11
FINANCIAL YEAR 2009-10

TAXPAYER DETAILS:
Name: Arjun Patil
PAN: **********
Address: 456, FC Road, Pune, Maharashtra - 411005
Phone: +91-**********
Email: <EMAIL>

INCOME DETAILS:
1. SALARY INCOME:
   Basic Salary                         ₹3,60,000
   House Rent Allowance                 ₹1,44,000
   Special Allowance                    ₹96,000
   Gross Salary                         ₹6,00,000

   Less: Standard Deduction             ₹40,000
   Net Salary Income                    ₹5,60,000

2. OTHER INCOME:
   Interest from Bank FD                ₹12,000
   Interest from Savings Account        ₹3,500
   Freelance Income                     ₹48,000
   Total Other Income                   ₹63,500

TOTAL GROSS INCOME                      ₹6,23,500

DEDUCTIONS UNDER CHAPTER VI-A:
Section 80C:
  - Provident Fund                      ₹43,200
  - Life Insurance Premium              ₹25,000
  - ELSS Mutual Funds                   ₹20,000
  - Fixed Deposit (5 years)             ₹11,800
  Total 80C                             ₹1,00,000

Section 80D:
  - Health Insurance Premium            ₹15,000

Section 80G:
  - Donation to PM Relief Fund          ₹5,000

TOTAL DEDUCTIONS                        ₹1,20,000

TAXABLE INCOME                          ₹5,03,500

TAX CALCULATION:
Income up to ₹1,60,000                  Nil
Income ₹1,60,001 to ₹5,00,000          ₹34,000 (10%)
Income ₹5,00,001 to ₹5,03,500          ₹700 (20%)
Total Tax                               ₹34,700

Add: Education Cess (3%)                ₹1,041
TOTAL TAX LIABILITY                     ₹35,741

TAX PAID:
TDS from Salary                         ₹28,000
Advance Tax Paid                        ₹8,000
Total Tax Paid                          ₹36,000

REFUND DUE                              ₹259

VERIFICATION:
I, Arjun Patil, son of Late Shri Ramesh Patil,
do hereby certify that the information given above is correct
and complete to the best of my knowledge and belief.

Place: Pune
Date: April 12, 2010
Signature: Arjun Patil

DOCUMENTS ATTACHED:
1. Form 16 from Infosys Technologies Ltd.
2. Bank Interest Certificates
3. Insurance Premium Receipts
4. Investment Proofs (80C)
5. Donation Receipts

Note: This return was filed electronically on April 12, 2010
ITR-V acknowledgment number: ***************`
                },
                'vacation_photos.jpg': {
                    type: 'image',
                    size: 2048576,
                    created: new Date('2010-07-15T09:15:00'),
                    modified: new Date('2010-07-15T09:15:00'),
                    extension: 'jpg'
                },
                'Decode.exe': {
                    type: 'executable',
                    size: 245760,
                    created: new Date('2010-03-20T14:30:00'),
                    modified: new Date('2010-08-10T16:45:00'),
                    extension: 'exe',
                    isDecoder: true
                },
                'Documents and Settings': {
                    type: 'folder',
                    created: new Date('2010-03-15T10:30:00'),
                    modified: new Date('2010-08-22T14:45:00'),
                    contents: {
                        'Administrator': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-08-22T14:45:00'),
                            contents: {
                                'Desktop': {
                                    type: 'folder',
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-08-22T14:45:00'),
                                    contents: {
                                        'My Computer.lnk': {
                                            type: 'shortcut',
                                            size: 1024,
                                            created: new Date('2010-03-15T10:30:00'),
                                            modified: new Date('2010-03-15T10:30:00'),
                                            extension: 'lnk'
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                'Program Files': {
                    type: 'folder',
                    created: new Date('2010-03-15T10:30:00'),
                    modified: new Date('2010-07-10T12:15:00'),
                    contents: {
                        'Internet Explorer': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-03-15T10:30:00'),
                            contents: {}
                        }
                    }
                },
                'Windows': {
                    type: 'folder',
                    created: new Date('2010-03-15T10:30:00'),
                    modified: new Date('2010-08-01T09:00:00'),
                    contents: {
                        'System32': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-08-01T09:00:00'),
                            contents: {}
                        }
                    }
                }
            }
        },
        'D:': {
            name: 'Data Drive (D:)',
            type: 'hard-drive',
            totalSpace: 120000000000, // 120 GB
            usedSpace: 25000000000,   // 25 GB
            fileSystem: 'NTFS',
            contents: {
                'favorite_song.mp3': {
                    type: 'audio',
                    size: 4567890,
                    created: new Date('2010-05-10T20:15:00'),
                    modified: new Date('2010-05-10T20:15:00'),
                    extension: 'mp3'
                },
                'home_movie.avi': {
                    type: 'video',
                    size: 157286400,
                    created: new Date('2010-07-20T21:00:00'),
                    modified: new Date('2010-07-20T21:00:00'),
                    extension: 'avi'
                },
                'system_backup.zip': {
                    type: 'archive',
                    size: 1073741824,
                    created: new Date('2010-08-01T02:00:00'),
                    modified: new Date('2010-08-01T02:00:00'),
                    extension: 'zip'
                },
                'Music': {
                    type: 'folder',
                    created: new Date('2010-04-01T12:00:00'),
                    modified: new Date('2010-08-15T18:30:00'),
                    contents: {
                        'classical_music.mp3': {
                            type: 'audio',
                            size: 6789012,
                            created: new Date('2010-06-05T15:45:00'),
                            modified: new Date('2010-06-05T15:45:00'),
                            extension: 'mp3'
                        }
                    }
                }
            }
        },

    },
    
    currentPath: ['My Computer'],
    
    getFileIcon(item) {
        // Return SVG markup instead of PNG path
        if (typeof SvgIcons !== 'undefined') {
            return SvgIcons.getFileIcon(item.type);
        }

        // Fallback to PNG if SVG system not available
        if (item.type === 'folder') return 'assets/icons/files/folder.png';

        const iconMap = {
            'image': 'assets/icons/files/image.png',
            'document': 'assets/icons/files/document.png',
            'spreadsheet': 'assets/icons/files/spreadsheet.png',
            'pdf': 'assets/icons/files/pdf.png',
            'executable': 'assets/icons/files/executable.png',
            'audio': 'assets/icons/files/audio.png',
            'video': 'assets/icons/files/video.png',
            'archive': 'assets/icons/files/archive.png',
            'shortcut': 'assets/icons/files/shortcut.png',
            'text': 'assets/icons/files/text.png',
            'web': 'assets/icons/files/web.png',
            'system': 'assets/icons/files/system.png'
        };

        return iconMap[item.type] || 'assets/icons/files/unknown.png';
    },

    getDriveIcon(driveType) {
        // Return SVG markup instead of PNG path
        if (typeof SvgIcons !== 'undefined') {
            return SvgIcons.getDriveIcon(driveType);
        }

        // Fallback to PNG if SVG system not available
        const iconMap = {
            'hard-drive': 'assets/icons/drives/hard-drive.png',
            'cd-drive': 'assets/icons/drives/cd-drive.png',
            'floppy-drive': 'assets/icons/drives/floppy-drive.png',
            'network-drive': 'assets/icons/drives/network-drive.png'
        };

        return iconMap[driveType] || 'assets/icons/drives/unknown.png';
    },
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 bytes';
        
        const k = 1024;
        const sizes = ['bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatDate(date) {
        return date.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        }) + ' ' + date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    }
};

// Make fileSystem globally available
window.fileSystem = fileSystem;
