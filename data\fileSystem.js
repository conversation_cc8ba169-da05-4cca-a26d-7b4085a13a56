// Mock File System Data for Windows XP File Manager

const fileSystem = {
    drives: {
        'C:': {
            name: 'Local Disk (C:)',
            type: 'hard-drive',
            totalSpace: 80000000000, // 80 GB
            usedSpace: 45000000000,  // 45 GB
            fileSystem: 'NTFS',
            contents: {
                'Documents and Settings': {
                    type: 'folder',
                    created: new Date('2010-03-15T10:30:00'),
                    modified: new Date('2010-08-22T14:45:00'),
                    contents: {
                        'Administrator': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-08-22T14:45:00'),
                            contents: {
                                'My Documents': {
                                    type: 'folder',
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-08-20T16:20:00'),
                                    contents: {
                                        'vacation_photos.jpg': {
                                            type: 'image',
                                            size: 2048576,
                                            created: new Date('2010-07-15T09:15:00'),
                                            modified: new Date('2010-07-15T09:15:00'),
                                            extension: 'jpg'
                                        },
                                        'family_reunion.jpg': {
                                            type: 'image',
                                            size: 1536000,
                                            created: new Date('2010-06-20T14:30:00'),
                                            modified: new Date('2010-06-20T14:30:00'),
                                            extension: 'jpg'
                                        },
                                        'budget_2010.xls': {
                                            type: 'spreadsheet',
                                            size: 45056,
                                            created: new Date('2010-01-05T08:00:00'),
                                            modified: new Date('2010-08-15T17:30:00'),
                                            extension: 'xls'
                                        },
                                        'resume.doc': {
                                            type: 'document',
                                            size: 28672,
                                            created: new Date('2010-02-10T11:45:00'),
                                            modified: new Date('2010-05-18T13:20:00'),
                                            extension: 'doc'
                                        },
                                        'tax_documents.pdf': {
                                            type: 'pdf',
                                            size: 156789,
                                            created: new Date('2010-04-12T16:00:00'),
                                            modified: new Date('2010-04-12T16:00:00'),
                                            extension: 'pdf'
                                        }
                                    }
                                },
                                'Desktop': {
                                    type: 'folder',
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-08-22T14:45:00'),
                                    contents: {
                                        'Recycle Bin.lnk': {
                                            type: 'shortcut',
                                            size: 1024,
                                            created: new Date('2010-03-15T10:30:00'),
                                            modified: new Date('2010-03-15T10:30:00'),
                                            extension: 'lnk'
                                        },
                                        'My Computer.lnk': {
                                            type: 'shortcut',
                                            size: 1024,
                                            created: new Date('2010-03-15T10:30:00'),
                                            modified: new Date('2010-03-15T10:30:00'),
                                            extension: 'lnk'
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                'Program Files': {
                    type: 'folder',
                    created: new Date('2010-03-15T10:30:00'),
                    modified: new Date('2010-07-10T12:15:00'),
                    contents: {
                        'Internet Explorer': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-03-15T10:30:00'),
                            contents: {
                                'iexplore.exe': {
                                    type: 'executable',
                                    size: 93184,
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-03-15T10:30:00'),
                                    extension: 'exe'
                                }
                            }
                        },
                        'Windows Media Player': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-03-15T10:30:00'),
                            contents: {
                                'wmplayer.exe': {
                                    type: 'executable',
                                    size: 135168,
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-03-15T10:30:00'),
                                    extension: 'exe'
                                }
                            }
                        }
                    }
                },
                'Windows': {
                    type: 'folder',
                    created: new Date('2010-03-15T10:30:00'),
                    modified: new Date('2010-08-01T09:00:00'),
                    contents: {
                        'System32': {
                            type: 'folder',
                            created: new Date('2010-03-15T10:30:00'),
                            modified: new Date('2010-08-01T09:00:00'),
                            contents: {
                                'notepad.exe': {
                                    type: 'executable',
                                    size: 69120,
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-03-15T10:30:00'),
                                    extension: 'exe'
                                },
                                'calc.exe': {
                                    type: 'executable',
                                    size: 114688,
                                    created: new Date('2010-03-15T10:30:00'),
                                    modified: new Date('2010-03-15T10:30:00'),
                                    extension: 'exe'
                                }
                            }
                        }
                    }
                }
            }
        },
        'D:': {
            name: 'Data Drive (D:)',
            type: 'hard-drive',
            totalSpace: 120000000000, // 120 GB
            usedSpace: 85000000000,   // 85 GB
            fileSystem: 'NTFS',
            contents: {
                'Music': {
                    type: 'folder',
                    created: new Date('2010-04-01T12:00:00'),
                    modified: new Date('2010-08-15T18:30:00'),
                    contents: {
                        'favorite_song.mp3': {
                            type: 'audio',
                            size: 4567890,
                            created: new Date('2010-05-10T20:15:00'),
                            modified: new Date('2010-05-10T20:15:00'),
                            extension: 'mp3'
                        },
                        'classical_music.mp3': {
                            type: 'audio',
                            size: 6789012,
                            created: new Date('2010-06-05T15:45:00'),
                            modified: new Date('2010-06-05T15:45:00'),
                            extension: 'mp3'
                        }
                    }
                },
                'Videos': {
                    type: 'folder',
                    created: new Date('2010-04-01T12:00:00'),
                    modified: new Date('2010-07-20T21:00:00'),
                    contents: {
                        'home_movie.avi': {
                            type: 'video',
                            size: 157286400,
                            created: new Date('2010-07-20T21:00:00'),
                            modified: new Date('2010-07-20T21:00:00'),
                            extension: 'avi'
                        },
                        'vacation_clip.wmv': {
                            type: 'video',
                            size: 89456123,
                            created: new Date('2010-07-18T19:30:00'),
                            modified: new Date('2010-07-18T19:30:00'),
                            extension: 'wmv'
                        }
                    }
                },
                'Backup': {
                    type: 'folder',
                    created: new Date('2010-04-01T12:00:00'),
                    modified: new Date('2010-08-01T02:00:00'),
                    contents: {
                        'system_backup.zip': {
                            type: 'archive',
                            size: 1073741824,
                            created: new Date('2010-08-01T02:00:00'),
                            modified: new Date('2010-08-01T02:00:00'),
                            extension: 'zip'
                        }
                    }
                }
            }
        },
        'E:': {
            name: 'CD Drive (E:)',
            type: 'cd-drive',
            totalSpace: 700000000, // 700 MB
            usedSpace: 0,
            fileSystem: 'CDFS',
            contents: {}
        }
    },
    
    currentPath: ['My Computer'],
    
    getFileIcon(item) {
        if (item.type === 'folder') return 'assets/icons/files/folder.png';
        
        const iconMap = {
            'image': 'assets/icons/files/image.png',
            'document': 'assets/icons/files/document.png',
            'spreadsheet': 'assets/icons/files/spreadsheet.png',
            'pdf': 'assets/icons/files/pdf.png',
            'executable': 'assets/icons/files/executable.png',
            'audio': 'assets/icons/files/audio.png',
            'video': 'assets/icons/files/video.png',
            'archive': 'assets/icons/files/archive.png',
            'shortcut': 'assets/icons/files/shortcut.png'
        };
        
        return iconMap[item.type] || 'assets/icons/files/unknown.png';
    },
    
    getDriveIcon(driveType) {
        const iconMap = {
            'hard-drive': 'assets/icons/drives/hard-drive.png',
            'cd-drive': 'assets/icons/drives/cd-drive.png',
            'floppy-drive': 'assets/icons/drives/floppy-drive.png',
            'network-drive': 'assets/icons/drives/network-drive.png'
        };
        
        return iconMap[driveType] || 'assets/icons/drives/unknown.png';
    },
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 bytes';
        
        const k = 1024;
        const sizes = ['bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatDate(date) {
        return date.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        }) + ' ' + date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    }
};

// Make fileSystem globally available
window.fileSystem = fileSystem;
