// File Utility Functions for Windows XP File Manager

const FileUtils = {
    /**
     * Format file size in human readable format
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 bytes';
        if (bytes === undefined || bytes === null) return 'Unknown';
        
        const k = 1024;
        const sizes = ['bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        if (i === 0) {
            return bytes + ' ' + sizes[i];
        }
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * Get file type description from extension
     * @param {string} extension - File extension
     * @returns {string} File type description
     */
    getFileTypeDescription(extension) {
        const typeMap = {
            // Documents
            'txt': 'Text Document',
            'doc': 'Microsoft Word Document',
            'docx': 'Microsoft Word Document',
            'pdf': 'Adobe Acrobat Document',
            'rtf': 'Rich Text Format',
            
            // Spreadsheets
            'xls': 'Microsoft Excel Worksheet',
            'xlsx': 'Microsoft Excel Worksheet',
            'csv': 'Comma Separated Values',
            
            // Presentations
            'ppt': 'Microsoft PowerPoint Presentation',
            'pptx': 'Microsoft PowerPoint Presentation',
            
            // Images
            'jpg': 'JPEG Image',
            'jpeg': 'JPEG Image',
            'png': 'PNG Image',
            'gif': 'GIF Image',
            'bmp': 'Bitmap Image',
            'tiff': 'TIFF Image',
            'ico': 'Icon',
            
            // Audio
            'mp3': 'MP3 Audio File',
            'wav': 'Wave Audio File',
            'wma': 'Windows Media Audio',
            'ogg': 'Ogg Vorbis Audio',
            'flac': 'FLAC Audio',
            
            // Video
            'avi': 'AVI Video File',
            'mp4': 'MP4 Video File',
            'wmv': 'Windows Media Video',
            'mov': 'QuickTime Movie',
            'mkv': 'Matroska Video',
            
            // Archives
            'zip': 'ZIP Archive',
            'rar': 'RAR Archive',
            '7z': '7-Zip Archive',
            'tar': 'TAR Archive',
            'gz': 'GZIP Archive',
            
            // Executables
            'exe': 'Application',
            'msi': 'Windows Installer Package',
            'bat': 'Batch File',
            'cmd': 'Command Script',
            
            // Web
            'html': 'HTML Document',
            'htm': 'HTML Document',
            'css': 'Cascading Style Sheet',
            'js': 'JavaScript File',
            
            // System
            'dll': 'Dynamic Link Library',
            'sys': 'System File',
            'ini': 'Configuration Settings',
            'reg': 'Registry Entries',
            
            // Shortcuts
            'lnk': 'Shortcut'
        };
        
        return typeMap[extension?.toLowerCase()] || 'File';
    },
    
    /**
     * Get appropriate icon for file type
     * @param {Object} item - File or folder item
     * @returns {string} Icon path or SVG markup
     */
    getItemIcon(item) {
        // Use SVG icons if available
        if (typeof SvgIcons !== 'undefined') {
            return SvgIcons.getFileIcon(item.type);
        }

        // Fallback to PNG icons
        if (item.type === 'folder') {
            return 'assets/icons/files/folder.png';
        }

        const iconMap = {
            // File types
            'image': 'assets/icons/files/image.png',
            'document': 'assets/icons/files/document.png',
            'spreadsheet': 'assets/icons/files/spreadsheet.png',
            'pdf': 'assets/icons/files/pdf.png',
            'executable': 'assets/icons/files/executable.png',
            'audio': 'assets/icons/files/audio.png',
            'video': 'assets/icons/files/video.png',
            'archive': 'assets/icons/files/archive.png',
            'shortcut': 'assets/icons/files/shortcut.png',
            'text': 'assets/icons/files/text.png',
            'web': 'assets/icons/files/web.png',
            'system': 'assets/icons/files/system.png'
        };

        return iconMap[item.type] || 'assets/icons/files/unknown.png';
    },
    
    /**
     * Check if file is an image
     * @param {string} extension - File extension
     * @returns {boolean} True if file is an image
     */
    isImage(extension) {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'ico', 'webp'];
        return imageExtensions.includes(extension?.toLowerCase());
    },
    
    /**
     * Check if file is a video
     * @param {string} extension - File extension
     * @returns {boolean} True if file is a video
     */
    isVideo(extension) {
        const videoExtensions = ['avi', 'mp4', 'wmv', 'mov', 'mkv', 'flv', 'webm', 'm4v'];
        return videoExtensions.includes(extension?.toLowerCase());
    },
    
    /**
     * Check if file is audio
     * @param {string} extension - File extension
     * @returns {boolean} True if file is audio
     */
    isAudio(extension) {
        const audioExtensions = ['mp3', 'wav', 'wma', 'ogg', 'flac', 'aac', 'm4a'];
        return audioExtensions.includes(extension?.toLowerCase());
    },
    
    /**
     * Check if file is a document
     * @param {string} extension - File extension
     * @returns {boolean} True if file is a document
     */
    isDocument(extension) {
        const docExtensions = ['txt', 'doc', 'docx', 'rtf', 'odt'];
        return docExtensions.includes(extension?.toLowerCase());
    },

    /**
     * Check if file is a spreadsheet
     * @param {string} extension - File extension
     * @returns {boolean} True if file is a spreadsheet
     */
    isSpreadsheet(extension) {
        const spreadsheetExtensions = ['xls', 'xlsx', 'csv', 'ods'];
        return spreadsheetExtensions.includes(extension?.toLowerCase());
    },
    
    /**
     * Check if file is executable
     * @param {string} extension - File extension
     * @returns {boolean} True if file is executable
     */
    isExecutable(extension) {
        const execExtensions = ['exe', 'msi', 'bat', 'cmd', 'com', 'scr'];
        return execExtensions.includes(extension?.toLowerCase());
    },
    
    /**
     * Generate a safe filename
     * @param {string} filename - Original filename
     * @returns {string} Safe filename
     */
    sanitizeFilename(filename) {
        // Remove or replace invalid characters
        return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
    },
    
    /**
     * Extract filename without extension
     * @param {string} filename - Full filename
     * @returns {string} Filename without extension
     */
    getFilenameWithoutExtension(filename) {
        const lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex === -1 || lastDotIndex === 0) {
            return filename;
        }
        return filename.substring(0, lastDotIndex);
    },
    
    /**
     * Extract file extension
     * @param {string} filename - Full filename
     * @returns {string} File extension (without dot)
     */
    getFileExtension(filename) {
        const lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
            return '';
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    },
    
    /**
     * Sort files and folders
     * @param {Array} items - Array of file/folder items
     * @param {string} sortBy - Sort criteria ('name', 'size', 'date', 'type')
     * @param {boolean} ascending - Sort direction
     * @returns {Array} Sorted array
     */
    sortItems(items, sortBy = 'name', ascending = true) {
        const sorted = [...items].sort((a, b) => {
            // Always put folders first
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            
            let comparison = 0;
            
            switch (sortBy) {
                case 'name':
                    comparison = a.name.localeCompare(b.name, undefined, { numeric: true });
                    break;
                case 'size':
                    comparison = (a.size || 0) - (b.size || 0);
                    break;
                case 'date':
                    comparison = (a.modified || new Date(0)) - (b.modified || new Date(0));
                    break;
                case 'type':
                    const aType = FileUtils.getFileTypeDescription(a.extension);
                    const bType = FileUtils.getFileTypeDescription(b.extension);
                    comparison = aType.localeCompare(bType);
                    break;
                default:
                    comparison = a.name.localeCompare(b.name, undefined, { numeric: true });
            }
            
            return ascending ? comparison : -comparison;
        });
        
        return sorted;
    },
    
    /**
     * Filter items by search query
     * @param {Array} items - Array of file/folder items
     * @param {string} query - Search query
     * @returns {Array} Filtered array
     */
    filterItems(items, query) {
        if (!query || query.trim() === '') {
            return items;
        }
        
        const searchTerm = query.toLowerCase().trim();
        
        return items.filter(item => {
            return item.name.toLowerCase().includes(searchTerm) ||
                   FileUtils.getFileTypeDescription(item.extension).toLowerCase().includes(searchTerm);
        });
    }
};

// Make FileUtils globally available
window.FileUtils = FileUtils;
