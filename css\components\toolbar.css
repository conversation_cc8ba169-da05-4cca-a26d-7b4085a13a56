/* Toolbar Component Styles */

.toolbar {
    height: 40px;
    background: linear-gradient(to bottom, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 100%);
    border-bottom: 1px solid #919b9c;
    display: flex;
    align-items: center;
    padding: 0 8px;
    gap: 4px;
}

.toolbar-btn {
    width: 32px;
    height: 32px;
    border: 1px solid transparent;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    padding: 2px;
}

.toolbar-btn:hover {
    border: 1px solid #316ac5;
    background: linear-gradient(to bottom, #e3f2fd 0%, #bbdefb 100%);
}

.toolbar-btn:active {
    border: 1px inset #316ac5;
    background: linear-gradient(to bottom, #90caf9 0%, #64b5f6 100%);
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
    border: 1px solid transparent;
    background: transparent;
}

.toolbar-btn img {
    width: 24px;
    height: 24px;
    pointer-events: none;
}

.separator {
    width: 1px;
    height: 24px;
    background: linear-gradient(to bottom, #919b9c 0%, #ffffff 50%, #919b9c 100%);
    margin: 0 4px;
}

.address-bar {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    max-width: 500px;
}

.address-bar label {
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #000;
    white-space: nowrap;
}

#address-input {
    flex: 1;
    height: 22px;
    border: 2px inset #ece9d8;
    padding: 2px 6px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    background: white;
    color: #000;
    margin-right: 6px;
}

.nav-btn {
    border: none;
    background: transparent;
    cursor: pointer;
    padding: 2px;
    margin: 0 2px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-btn:active {
    transform: translateY(1px);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-btn:disabled:hover {
    background: transparent;
    transform: none;
}

.nav-btn svg {
    display: block;
}

#address-input:focus {
    outline: none;
    border-color: #316ac5;
}

#address-input:read-only {
    background: #f0f0f0;
    color: #666;
}

/* Toolbar Button Tooltips */
.toolbar-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: #ffffe1;
    border: 1px solid #000;
    padding: 2px 6px;
    font-size: 11px;
    font-family: 'Tahoma', sans-serif;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    pointer-events: none;
}

/* Dropdown styles for view button */
.view-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: #f0f0f0;
    border: 2px outset #f0f0f0;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
    min-width: 120px;
    z-index: 100;
    display: none;
}

.view-dropdown.show {
    display: block;
}

.view-dropdown-item {
    padding: 6px 12px;
    cursor: pointer;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    border-bottom: 1px solid #d0d0d0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-dropdown-item:last-child {
    border-bottom: none;
}

.view-dropdown-item:hover {
    background: #316ac5;
    color: white;
}

.view-dropdown-item.selected {
    background: #e0e0e0;
    font-weight: bold;
}

.view-dropdown-item.selected::before {
    content: '●';
    color: #000;
}

/* Navigation button states */
#back-btn:disabled,
#forward-btn:disabled,
#up-btn:disabled {
    opacity: 0.4;
}

/* Toolbar responsive behavior */
@media (max-width: 800px) {
    .address-bar {
        max-width: 200px;
    }
    
    .address-bar label {
        display: none;
    }
}

@media (max-width: 600px) {
    .toolbar {
        flex-wrap: wrap;
        height: auto;
        min-height: 40px;
        padding: 4px;
    }
    
    .address-bar {
        order: 1;
        width: 100%;
        max-width: none;
        margin-top: 4px;
    }
}
