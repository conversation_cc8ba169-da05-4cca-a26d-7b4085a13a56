/* Details Panel Component Styles */

.details-panel {
    width: 220px;
    background: #ece9d8;
    border-left: 1px solid #919b9c;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.details-header {
    background: linear-gradient(to bottom, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 100%);
    border-bottom: 1px solid #919b9c;
    padding: 6px 8px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    font-weight: bold;
    color: #000;
}

.details-content {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
}

/* File Details */
.file-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    background: white;
    border: 2px inset #ece9d8;
    border-radius: 2px;
}

.file-preview-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 6px;
}

.file-preview-name {
    font-weight: bold;
    text-align: center;
    word-wrap: break-word;
    max-width: 100%;
    line-height: 1.2;
}

.file-properties {
    background: white;
    border: 2px inset #ece9d8;
    border-radius: 2px;
    padding: 8px;
}

.file-properties h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: bold;
    color: #000;
    border-bottom: 1px solid #d0d0d0;
    padding-bottom: 4px;
}

.property-row {
    display: flex;
    margin-bottom: 6px;
    align-items: flex-start;
}

.property-label {
    font-weight: bold;
    min-width: 70px;
    color: #333;
    margin-right: 8px;
}

.property-value {
    flex: 1;
    color: #000;
    word-wrap: break-word;
}

/* Image Preview */
.image-preview {
    max-width: 100%;
    max-height: 120px;
    border: 1px solid #d0d0d0;
    margin-bottom: 6px;
}

/* Folder Details */
.folder-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.folder-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    background: white;
    border: 2px inset #ece9d8;
    border-radius: 2px;
}

.folder-preview-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 6px;
}

.folder-preview-name {
    font-weight: bold;
    text-align: center;
    word-wrap: break-word;
    max-width: 100%;
    line-height: 1.2;
}

.folder-stats {
    background: white;
    border: 2px inset #ece9d8;
    border-radius: 2px;
    padding: 8px;
}

.folder-stats h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: bold;
    color: #000;
    border-bottom: 1px solid #d0d0d0;
    padding-bottom: 4px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.stat-label {
    color: #333;
}

.stat-value {
    font-weight: bold;
    color: #000;
}

/* Drive Details */
.drive-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.drive-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    background: white;
    border: 2px inset #ece9d8;
    border-radius: 2px;
}

.drive-preview-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 6px;
}

.drive-preview-name {
    font-weight: bold;
    text-align: center;
    word-wrap: break-word;
    max-width: 100%;
    line-height: 1.2;
    margin-bottom: 2px;
}

.drive-preview-label {
    font-size: 10px;
    color: #666;
    text-align: center;
}

.drive-space {
    background: white;
    border: 2px inset #ece9d8;
    border-radius: 2px;
    padding: 8px;
}

.drive-space h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: bold;
    color: #000;
    border-bottom: 1px solid #d0d0d0;
    padding-bottom: 4px;
}

.space-bar {
    height: 16px;
    border: 2px inset #ece9d8;
    background: white;
    margin: 6px 0;
    overflow: hidden;
    position: relative;
}

.space-used {
    height: 100%;
    background: linear-gradient(to right, #ff6b6b 0%, #ff8e8e 100%);
    transition: width 0.3s ease;
}

.space-info {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #666;
    margin-top: 4px;
}

/* Empty State */
.details-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    text-align: center;
    padding: 20px;
}

.details-empty-icon {
    width: 32px;
    height: 32px;
    opacity: 0.5;
    margin-bottom: 8px;
}

/* Scrollbar for Details Panel */
.details-content::-webkit-scrollbar {
    width: 16px;
}

.details-content::-webkit-scrollbar-track {
    background: #ece9d8;
    border: 1px inset #ece9d8;
}

.details-content::-webkit-scrollbar-thumb {
    background: #d4d0c8;
    border: 1px outset #d4d0c8;
}

.details-content::-webkit-scrollbar-thumb:hover {
    background: #c0c0c0;
}

/* Responsive Details Panel */
@media (max-width: 1024px) {
    .details-panel {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .details-panel {
        display: none;
    }
    
    .details-panel.mobile-visible {
        display: flex;
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        z-index: 100;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
    }
}

/* Details Toggle Button (for mobile) */
.details-toggle {
    display: none;
    width: 32px;
    height: 32px;
    border: 1px solid transparent;
    background: transparent;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.details-toggle:hover {
    border: 1px solid #316ac5;
    background: linear-gradient(to bottom, #e3f2fd 0%, #bbdefb 100%);
}

@media (max-width: 768px) {
    .details-toggle {
        display: flex;
    }
}
