/* Windows XP Theme Specific Styles */

/* XP <PERSON>ton Styles */
.xp-button {
    background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 50%, #e0e0e0 51%, #d0d0d0 100%);
    border: 1px solid #0078d4;
    border-radius: 3px;
    padding: 3px 12px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    cursor: pointer;
    color: #000;
}

.xp-button:hover {
    background: linear-gradient(to bottom, #e3f2fd 0%, #bbdefb 50%, #90caf9 51%, #64b5f6 100%);
    border-color: #1976d2;
}

.xp-button:active {
    background: linear-gradient(to bottom, #64b5f6 0%, #90caf9 50%, #bbdefb 51%, #e3f2fd 100%);
    border: 1px inset #0078d4;
}

/* XP Input Styles */
.xp-input {
    border: 2px inset #ece9d8;
    padding: 2px 4px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    background: white;
}

.xp-input:focus {
    outline: none;
    border-color: #316ac5;
}

/* XP List Styles */
.xp-listbox {
    border: 2px inset #ece9d8;
    background: white;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
}

/* XP Panel Styles */
.xp-panel {
    background: #ece9d8;
    border: 1px solid #919b9c;
    padding: 4px;
}

.xp-panel-inset {
    border: 2px inset #ece9d8;
    background: white;
}

.xp-panel-outset {
    border: 2px outset #ece9d8;
    background: #ece9d8;
}

/* XP Separator */
.xp-separator {
    width: 1px;
    height: 20px;
    background: linear-gradient(to bottom, #919b9c 0%, #ffffff 50%, #919b9c 100%);
    margin: 0 4px;
}

/* XP Groupbox */
.xp-groupbox {
    border: 2px groove #ece9d8;
    padding: 8px;
    margin: 4px;
    background: #ece9d8;
}

.xp-groupbox-title {
    background: #ece9d8;
    padding: 0 4px;
    font-weight: bold;
    position: relative;
    top: -16px;
    left: 8px;
    display: inline-block;
}

/* XP Link Styles */
.xp-link {
    color: #0000ee;
    text-decoration: underline;
    cursor: pointer;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
}

.xp-link:hover {
    color: #ff0000;
}

.xp-link:visited {
    color: #551a8b;
}

/* XP Selection Styles */
.xp-selected {
    background: #316ac5;
    color: white;
}

.xp-selected:focus {
    background: #0078d4;
}

/* XP Disabled Styles */
.xp-disabled {
    color: #6d6d6d;
    background: #f0f0f0;
}

/* XP Progress Bar */
.xp-progress {
    border: 2px inset #ece9d8;
    height: 20px;
    background: white;
    overflow: hidden;
}

.xp-progress-bar {
    height: 100%;
    background: linear-gradient(to right, #0078d4 0%, #64b5f6 50%, #0078d4 100%);
    background-size: 20px 20px;
    animation: xp-progress-animation 1s linear infinite;
}

@keyframes xp-progress-animation {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* XP Tooltip */
.xp-tooltip {
    background: #ffffe1;
    border: 1px solid #000;
    padding: 2px 4px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    position: absolute;
    z-index: 1000;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* XP Context Menu */
.xp-context-menu {
    background: #f0f0f0;
    border: 2px outset #f0f0f0;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
    position: absolute;
    z-index: 1000;
    min-width: 150px;
}

.xp-context-menu-item {
    padding: 4px 20px 4px 8px;
    cursor: pointer;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    border-bottom: 1px solid #d0d0d0;
}

.xp-context-menu-item:hover {
    background: #316ac5;
    color: white;
}

.xp-context-menu-separator {
    height: 1px;
    background: #d0d0d0;
    margin: 2px 0;
}

/* XP Checkbox */
.xp-checkbox {
    width: 13px;
    height: 13px;
    border: 2px inset #ece9d8;
    background: white;
    cursor: pointer;
    position: relative;
}

.xp-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 1px;
    font-size: 11px;
    color: #000;
}

/* XP Radio Button */
.xp-radio {
    width: 13px;
    height: 13px;
    border: 2px inset #ece9d8;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    position: relative;
}

.xp-radio:checked::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: #000;
}
