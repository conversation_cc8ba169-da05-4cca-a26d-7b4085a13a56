# SVG Icon System

The Windows XP File Manager now uses a comprehensive SVG icon system instead of PNG files. This provides better scalability, smaller file sizes, and consistent rendering across different screen resolutions.

## Features

### Drive Icons
- **Hard Drive**: Realistic hard drive with connection ports and screws
- **CD Drive**: CD/DVD drive with disc and slot details
- **Floppy Drive**: Classic 3.5" floppy disk with authentic blue color
- **Network Drive**: Network drive with connection indicators

### File Type Icons
- **Folder**: Classic yellow folder with depth effect
- **Document**: White document with folded corner and text lines
- **Spreadsheet**: Green spreadsheet with grid and chart elements
- **PDF**: Red PDF document with "PDF" text
- **Image**: Picture frame with mountain landscape and sun
- **Audio**: Purple audio file with musical note
- **Video**: Red video file with play button
- **Executable**: Gray executable with gear icon
- **Archive**: Orange archive file with zipper effect
- **Text**: White text file with multiple text lines
- **Web**: Blue web file with globe icon
- **System**: Brown system file with warning triangle
- **Shortcut**: White file with arrow and link symbol
- **Unknown**: Gray file with question mark

## Implementation

### SVG Icon System (`js/svgIcons.js`)
The `SvgIcons` object contains all SVG definitions and provides methods to retrieve icons:

```javascript
// Get drive icon
const driveIcon = SvgIcons.getDriveIcon('hard-drive');

// Get file icon
const fileIcon = SvgIcons.getFileIcon('document');
```

### Integration
The system automatically detects if SVG icons are available and falls back to PNG files if needed:

1. **fileSystem.js**: Updated to return SVG markup when available
2. **fileManager.js**: Modified to handle both SVG and IMG elements
3. **CSS**: Updated to properly style SVG icons at different sizes

### Icon Sizes
Icons automatically scale to appropriate sizes:
- **Icon View**: 32x32px
- **List/Details View**: 16x16px
- **Drive Icons**: 48x48px
- **Details Panel**: 48x48px

## Benefits

1. **Scalability**: Vector graphics scale perfectly at any size
2. **Performance**: No external image files to load
3. **Consistency**: Uniform rendering across all browsers
4. **Customization**: Easy to modify colors and styles via CSS
5. **Windows XP Authentic**: Designed to match the original XP aesthetic
6. **Simplified Design**: Removed complex gradients to prevent ID conflicts

## Browser Support

The SVG icon system works in all modern browsers that support:
- SVG rendering
- CSS Flexbox
- ES6 JavaScript

## Fallback System

If SVG icons are not available (e.g., if the script fails to load), the system automatically falls back to:
1. PNG image files (if available)
2. Emoji icons as final fallback

This ensures the file manager always displays some form of visual representation for files and drives.
