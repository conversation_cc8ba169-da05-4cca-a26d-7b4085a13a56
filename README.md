# Windows XP File Manager

A faithful recreation of the classic Windows XP file manager interface using modern web technologies.

## Features

- **Authentic Windows XP Styling**: Classic blue theme, Tahoma fonts, and XP-style UI elements
- **SVG Icon System**: Beautiful, scalable vector icons for all file types and drives
- **Drive Navigation**: Browse different drives (C:, D:, E:) with realistic file structures
- **Multiple View Modes**: Icon view, List view, and Details view
- **File Operations**: Navigate folders, view file details, and open files in popup viewers
- **Details Panel**: Shows file properties, creation/modification dates, and file sizes
- **Navigation History**: Back/Forward buttons with navigation history
- **Responsive Design**: Adapts to different screen sizes while maintaining XP aesthetics

## Project Structure

```
FileManager/
├── index.html                 # Main HTML file
├── css/                      # Stylesheets
│   ├── main.css             # Main styles and layout
│   ├── xp-theme.css         # Windows XP theme styles
│   └── components/          # Component-specific styles
│       ├── toolbar.css      # Toolbar styling
│       ├── sidebar.css      # Sidebar styling
│       ├── file-list.css    # File list styling
│       └── details-panel.css # Details panel styling
├── js/                      # JavaScript files
│   ├── main.js             # Main initialization
│   ├── fileManager.js      # Core file manager logic
│   ├── fileViewer.js       # File popup viewer
│   ├── svgIcons.js         # SVG icon system
│   └── utils/              # Utility functions
│       ├── dateUtils.js    # Date formatting utilities
│       └── fileUtils.js    # File handling utilities
├── data/                   # Data files
│   └── fileSystem.js       # Mock file system data
├── assets/                 # Static assets
│   ├── icons/             # Icon files (placeholders)
│   └── sample-files/      # Sample files for preview
└── README.md              # This file
```

## File System

The application includes a mock file system with:

- **C: Drive**: System drive with Windows folders, Program Files, and user documents
- **D: Drive**: Data drive with Music, Videos, and Backup folders
- **E: Drive**: CD/DVD drive (empty)

All files are dated around 2010 to maintain the Windows XP era authenticity.

## SVG Icon System

The file manager features a comprehensive SVG icon system with:

- **Drive Icons**: Hard drives, CD drives, floppy drives, and network drives
- **File Type Icons**: Documents, spreadsheets, PDFs, images, audio, video, executables, archives, and more
- **Scalable Design**: Vector graphics that look crisp at any size
- **Windows XP Styling**: Authentic gradients, colors, and design elements
- **Fallback Support**: Automatic fallback to PNG images if SVG is unavailable

View all icons: Open `test-icons.html` to see a showcase of all available icons.

## File Types Supported

- **Documents**: .doc, .txt, .pdf, .rtf
- **Spreadsheets**: .xls, .csv
- **Images**: .jpg, .png, .gif, .bmp
- **Audio**: .mp3, .wav, .wma
- **Video**: .avi, .wmv, .mp4
- **Archives**: .zip, .rar, .7z
- **Executables**: .exe, .msi, .bat
- **Shortcuts**: .lnk

## Usage

1. Open `index.html` in a modern web browser
2. Navigate through drives by double-clicking on drive icons
3. Browse folders and files using the familiar Windows XP interface
4. Use the toolbar buttons for navigation (Back, Forward, Up)
5. Switch between view modes using the View button
6. Click on files to see details in the right panel
7. Double-click files to open them in the popup viewer

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

## Technical Details

- **Pure HTML/CSS/JavaScript**: No external frameworks or dependencies
- **Responsive Design**: CSS Grid and Flexbox for layout
- **Modern JavaScript**: ES6+ features with class-based architecture
- **Modular Structure**: Separated concerns with utility functions and components

## Customization

### Adding New Files
Edit `data/fileSystem.js` to add new files and folders to the mock file system.

### Changing Themes
Modify `css/xp-theme.css` to customize colors and styling while maintaining the XP aesthetic.

### Adding Icons
Place icon files in the `assets/icons/` directory following the naming convention described in `assets/icons/placeholder.txt`.

## Known Limitations

- File operations are simulated (no actual file system access)
- Some advanced Windows XP features are not implemented
- Network drives and advanced permissions are not simulated

## Future Enhancements

- Context menus with file operations
- Drag and drop functionality
- Search functionality
- Thumbnail previews for images
- Audio/video playback in file viewer
- More authentic Windows XP sounds and animations

## License

This project is for educational and demonstration purposes. Windows XP is a trademark of Microsoft Corporation.

## Contributing

Feel free to submit issues and enhancement requests!
