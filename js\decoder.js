// Decoder Application - PowerShell Style Command Line Interface

class DecoderApp {
    constructor() {
        this.overlay = document.getElementById('popup-overlay');
        this.window = this.overlay.querySelector('.popup-window');
        this.titleElement = document.getElementById('popup-title');
        this.contentElement = document.getElementById('popup-content');
        this.closeButton = document.getElementById('popup-close');
        
        this.commandHistory = [];
        this.historyIndex = -1;
        this.currentPath = 'C:\\Tools\\Decoder>';
        
        this.commands = {
            'help': this.showHelp.bind(this),
            'clear': this.clearScreen.bind(this),
            'cls': this.clearScreen.bind(this),
            'base64encode': this.base64Encode.bind(this),
            'base64decode': this.base64Decode.bind(this),
            'asciitobase64': this.asciiToBase64.bind(this),
            'base64toascii': this.base64ToAscii.bind(this),
            'test': this.runTests.bind(this),
            'about': this.showAbout.bind(this),
            'exit': this.close.bind(this)
        };
    }
    
    open() {
        this.titleElement.textContent = 'Decode.exe - Command Line Decoder';
        this.createTerminalInterface();
        this.overlay.style.display = 'flex';
        
        // Focus the input
        setTimeout(() => {
            const input = this.contentElement.querySelector('.terminal-input');
            if (input) input.focus();
        }, 100);
    }
    
    createTerminalInterface() {
        this.contentElement.innerHTML = '';
        this.contentElement.style.cssText = `
            background: #012456;
            color: #ffffff;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            border: 2px inset #ece9d8;
        `;
        
        const terminal = document.createElement('div');
        terminal.className = 'terminal-container';
        terminal.style.cssText = `
            height: 100%;
            display: flex;
            flex-direction: column;
        `;
        
        // Terminal output area
        const output = document.createElement('div');
        output.className = 'terminal-output';
        output.style.cssText = `
            flex: 1;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.4;
        `;
        
        // Welcome message
        output.innerHTML = `Microsoft Windows XP [Version 5.1.2600]
(C) Copyright 1985-2001 Microsoft Corp.

Decode.exe - Advanced Base64 Decoder Tool v1.0
Type 'help' for available commands.

`;
        
        // Input area
        const inputContainer = document.createElement('div');
        inputContainer.className = 'terminal-input-container';
        inputContainer.style.cssText = `
            display: flex;
            align-items: center;
            margin-top: 5px;
        `;
        
        const prompt = document.createElement('span');
        prompt.className = 'terminal-prompt';
        prompt.textContent = this.currentPath;
        prompt.style.color = '#ffffff';
        
        const input = document.createElement('input');
        input.className = 'terminal-input';
        input.type = 'text';
        input.style.cssText = `
            background: transparent;
            border: none;
            color: #ffffff;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            outline: none;
            flex: 1;
            margin-left: 5px;
        `;
        
        inputContainer.appendChild(prompt);
        inputContainer.appendChild(input);
        
        terminal.appendChild(output);
        terminal.appendChild(inputContainer);
        this.contentElement.appendChild(terminal);
        
        // Event listeners
        input.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Store references
        this.terminalOutput = output;
        this.terminalInput = input;
        this.terminalPrompt = prompt;
    }
    
    handleKeyDown(e) {
        if (e.key === 'Enter') {
            const command = this.terminalInput.value.trim();
            this.executeCommand(command);
            this.terminalInput.value = '';
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            this.navigateHistory(-1);
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.navigateHistory(1);
        }
    }
    
    navigateHistory(direction) {
        if (this.commandHistory.length === 0) return;
        
        this.historyIndex += direction;
        
        if (this.historyIndex < 0) {
            this.historyIndex = -1;
            this.terminalInput.value = '';
        } else if (this.historyIndex >= this.commandHistory.length) {
            this.historyIndex = this.commandHistory.length - 1;
        }
        
        if (this.historyIndex >= 0) {
            this.terminalInput.value = this.commandHistory[this.historyIndex];
        }
    }
    
    executeCommand(command) {
        if (!command) return;
        
        // Add to history
        this.commandHistory.push(command);
        this.historyIndex = this.commandHistory.length;
        
        // Display command
        this.addOutput(`${this.currentPath}${command}`);
        
        // Parse command and arguments
        const parts = command.split(' ');
        const cmd = parts[0].toLowerCase();
        const args = parts.slice(1);
        
        // Execute command
        if (this.commands[cmd]) {
            this.commands[cmd](args);
        } else {
            this.addOutput(`'${cmd}' is not recognized as an internal or external command.`);
        }
        
        this.addOutput(''); // Empty line
    }
    
    addOutput(text) {
        this.terminalOutput.textContent += text + '\n';
        this.terminalOutput.scrollTop = this.terminalOutput.scrollHeight;
    }
    
    showHelp() {
        const helpText = `Available Commands:
        
ENCODING/DECODING:
  base64encode <text>     - Encode text to Base64
  base64decode <base64>   - Decode Base64 to text
  asciitobase64 <text>    - Convert ASCII text to Base64
  base64toascii <base64>  - Convert Base64 to ASCII text

SYSTEM:
  help                    - Show this help message
  clear, cls              - Clear the screen
  test                    - Run decoder tests
  about                   - Show application information
  exit                    - Close the decoder
  
EXAMPLES:
  base64encode Hello World
  base64decode SGVsbG8gV29ybGQ=
  asciitobase64 Test123
  base64toascii VGVzdDEyMw==`;
        
        this.addOutput(helpText);
    }
    
    clearScreen() {
        this.terminalOutput.textContent = '';
    }
    
    base64Encode(args) {
        if (args.length === 0) {
            this.addOutput('Usage: base64encode <text>');
            return;
        }
        
        const text = args.join(' ');
        try {
            const encoded = btoa(text);
            this.addOutput(`Input:  ${text}`);
            this.addOutput(`Output: ${encoded}`);
        } catch (error) {
            this.addOutput(`Error: ${error.message}`);
        }
    }
    
    base64Decode(args) {
        if (args.length === 0) {
            this.addOutput('Usage: base64decode <base64>');
            return;
        }
        
        const base64 = args.join(' ');
        try {
            const decoded = atob(base64);
            this.addOutput(`Input:  ${base64}`);
            this.addOutput(`Output: ${decoded}`);
        } catch (error) {
            this.addOutput(`Error: Invalid Base64 string`);
        }
    }
    
    asciiToBase64(args) {
        if (args.length === 0) {
            this.addOutput('Usage: asciitobase64 <text>');
            return;
        }
        
        const text = args.join(' ');
        try {
            // Convert to ASCII codes first, then to Base64
            const asciiCodes = text.split('').map(char => char.charCodeAt(0)).join(' ');
            const encoded = btoa(text);
            this.addOutput(`Input:      ${text}`);
            this.addOutput(`ASCII:      ${asciiCodes}`);
            this.addOutput(`Base64:     ${encoded}`);
        } catch (error) {
            this.addOutput(`Error: ${error.message}`);
        }
    }
    
    base64ToAscii(args) {
        if (args.length === 0) {
            this.addOutput('Usage: base64toascii <base64>');
            return;
        }
        
        const base64 = args.join(' ');
        try {
            const decoded = atob(base64);
            const asciiCodes = decoded.split('').map(char => char.charCodeAt(0)).join(' ');
            this.addOutput(`Input:      ${base64}`);
            this.addOutput(`Decoded:    ${decoded}`);
            this.addOutput(`ASCII:      ${asciiCodes}`);
        } catch (error) {
            this.addOutput(`Error: Invalid Base64 string`);
        }
    }
    
    runTests() {
        this.addOutput('Running Base64 Decoder Tests...');
        this.addOutput('');

        const tests = [
            { input: 'Hello World', expected: 'SGVsbG8gV29ybGQ=' },
            { input: 'Arjun Patil', expected: 'QXJqdW4gUGF0aWw=' },
            { input: 'Test123', expected: 'VGVzdDEyMw==' },
            { input: 'Windows XP', expected: 'V2luZG93cyBYUA==' }
        ];

        let passed = 0;
        let failed = 0;

        tests.forEach((test, index) => {
            try {
                const encoded = btoa(test.input);
                const decoded = atob(encoded);

                if (encoded === test.expected && decoded === test.input) {
                    this.addOutput(`Test ${index + 1}: PASS - "${test.input}" <-> "${test.expected}"`);
                    passed++;
                } else {
                    this.addOutput(`Test ${index + 1}: FAIL - Expected: "${test.expected}", Got: "${encoded}"`);
                    failed++;
                }
            } catch (error) {
                this.addOutput(`Test ${index + 1}: ERROR - ${error.message}`);
                failed++;
            }
        });

        this.addOutput('');
        this.addOutput(`Test Results: ${passed} passed, ${failed} failed`);

        if (failed === 0) {
            this.addOutput('All tests passed! Decoder is working correctly.');
        }
    }

    showAbout() {
        const aboutText = `Decode.exe - Advanced Base64 Decoder Tool
Version: 1.0
Build Date: March 20, 2010
Author: Arjun Patil

DESCRIPTION:
This command-line tool provides comprehensive Base64 encoding and decoding
capabilities with ASCII conversion support. Built for Windows XP environment.

FEATURES:
- Base64 text encoding/decoding
- ASCII to Base64 conversion
- Base64 to ASCII conversion
- Command history navigation
- Built-in test suite
- PowerShell-style interface

SYSTEM REQUIREMENTS:
- Windows XP or later
- Internet Explorer 6.0 or later
- 256 MB RAM minimum

COPYRIGHT:
(C) 2010 Arjun Patil. All rights reserved.
This software is provided as-is without warranty.`;

        this.addOutput(aboutText);
    }

    close() {
        this.overlay.style.display = 'none';
        this.contentElement.innerHTML = '';
    }
}

// Make DecoderApp globally available
window.DecoderApp = DecoderApp;
