// Decoder Application - PowerShell Style Interface with Buttons

class DecoderApp {
    constructor() {
        this.overlay = document.getElementById('popup-overlay');
        this.window = this.overlay.querySelector('.popup-window');
        this.titleElement = document.getElementById('popup-title');
        this.contentElement = document.getElementById('popup-content');
        this.closeButton = document.getElementById('popup-close');

        this.currentMode = 'menu'; // 'menu', 'base64encode', 'base64decode', 'asciitobase64', 'base64toascii'
    }
    
    open() {
        this.titleElement.textContent = 'Decode.exe - Base64 Decoder Tool';
        this.currentMode = 'menu';
        this.createInterface();
        this.overlay.style.display = 'flex';
    }

    createInterface() {
        this.contentElement.innerHTML = '';
        this.contentElement.style.cssText = `
            background: #012456;
            color: #ffffff;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            height: 450px;
            overflow-y: auto;
            border: 2px inset #ece9d8;
        `;

        if (this.currentMode === 'menu') {
            this.createMainMenu();
        } else {
            this.createDecoderInterface();
        }
    }

    createMainMenu() {
        const container = document.createElement('div');
        container.style.cssText = `
            height: 100%;
            display: flex;
            flex-direction: column;
        `;

        // Header
        const header = document.createElement('div');
        header.innerHTML = `Microsoft Windows XP [Version 5.1.2600]
(C) Copyright 1985-2001 Microsoft Corp.

Decode.exe - Advanced Base64 Decoder Tool v1.0
Developed by Arjun Patil

═══════════════════════════════════════════════════════════════
                    DECODER SELECTION MENU
═══════════════════════════════════════════════════════════════

Select a decoder option:

`;
        header.style.cssText = `
            white-space: pre-wrap;
            margin-bottom: 20px;
            line-height: 1.4;
        `;

        // Button container
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        `;

        // Create buttons
        const buttons = [
            { id: 'base64encode', text: '[1] Base64 Encode - Convert Text to Base64', mode: 'base64encode' },
            { id: 'base64decode', text: '[2] Base64 Decode - Convert Base64 to Text', mode: 'base64decode' },
            { id: 'asciitobase64', text: '[3] ASCII to Base64 - Convert ASCII Text to Base64', mode: 'asciitobase64' },
            { id: 'base64toascii', text: '[4] Base64 to ASCII - Convert Base64 to ASCII Text', mode: 'base64toascii' }
        ];

        buttons.forEach(btn => {
            const button = document.createElement('button');
            button.textContent = btn.text;
            button.style.cssText = `
                background: #1e3a5f;
                color: #ffffff;
                border: 1px solid #4a90e2;
                padding: 8px 15px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                cursor: pointer;
                text-align: left;
                border-radius: 0;
            `;

            button.addEventListener('mouseover', () => {
                button.style.background = '#2a4a6f';
            });

            button.addEventListener('mouseout', () => {
                button.style.background = '#1e3a5f';
            });

            button.addEventListener('click', () => {
                this.currentMode = btn.mode;
                this.createInterface();
            });

            buttonContainer.appendChild(button);
        });

        // Footer
        const footer = document.createElement('div');
        footer.innerHTML = `

═══════════════════════════════════════════════════════════════
Press ESC or click Close to exit the application.
═══════════════════════════════════════════════════════════════`;
        footer.style.cssText = `
            white-space: pre-wrap;
            margin-top: auto;
            line-height: 1.4;
        `;

        container.appendChild(header);
        container.appendChild(buttonContainer);
        container.appendChild(footer);
        this.contentElement.appendChild(container);

        // ESC key handler
        document.addEventListener('keydown', this.handleEscKey.bind(this));
    }
    
    createDecoderInterface() {
        const container = document.createElement('div');
        container.style.cssText = `
            height: 100%;
            display: flex;
            flex-direction: column;
        `;

        // Header with title
        const header = document.createElement('div');
        const titles = {
            'base64encode': 'BASE64 ENCODER - Convert Text to Base64',
            'base64decode': 'BASE64 DECODER - Convert Base64 to Text',
            'asciitobase64': 'ASCII TO BASE64 - Convert ASCII Text to Base64',
            'base64toascii': 'BASE64 TO ASCII - Convert Base64 to ASCII Text'
        };

        header.innerHTML = `═══════════════════════════════════════════════════════════════
${titles[this.currentMode]}
═══════════════════════════════════════════════════════════════

`;
        header.style.cssText = `
            white-space: pre-wrap;
            margin-bottom: 15px;
            line-height: 1.4;
        `;

        // Input section
        const inputSection = document.createElement('div');
        inputSection.style.cssText = `
            margin-bottom: 15px;
        `;

        const inputLabel = document.createElement('div');
        inputLabel.textContent = 'Input:';
        inputLabel.style.cssText = `
            margin-bottom: 5px;
            font-weight: bold;
        `;

        const inputTextarea = document.createElement('textarea');
        inputTextarea.style.cssText = `
            width: 100%;
            height: 80px;
            background: #1e3a5f;
            color: #ffffff;
            border: 1px solid #4a90e2;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            padding: 8px;
            resize: vertical;
            box-sizing: border-box;
        `;
        inputTextarea.placeholder = this.getInputPlaceholder();

        inputSection.appendChild(inputLabel);
        inputSection.appendChild(inputTextarea);

        // Buttons section
        const buttonSection = document.createElement('div');
        buttonSection.style.cssText = `
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        `;

        const processBtn = document.createElement('button');
        processBtn.textContent = 'Process';
        processBtn.style.cssText = `
            background: #28a745;
            color: #ffffff;
            border: 1px solid #1e7e34;
            padding: 8px 20px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            cursor: pointer;
            border-radius: 0;
        `;

        const clearBtn = document.createElement('button');
        clearBtn.textContent = 'Clear';
        clearBtn.style.cssText = `
            background: #dc3545;
            color: #ffffff;
            border: 1px solid #c82333;
            padding: 8px 20px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            cursor: pointer;
            border-radius: 0;
        `;

        const backBtn = document.createElement('button');
        backBtn.textContent = 'Back to Menu';
        backBtn.style.cssText = `
            background: #6c757d;
            color: #ffffff;
            border: 1px solid #545b62;
            padding: 8px 20px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            cursor: pointer;
            border-radius: 0;
        `;

        buttonSection.appendChild(processBtn);
        buttonSection.appendChild(clearBtn);
        buttonSection.appendChild(backBtn);

        // Output section
        const outputSection = document.createElement('div');
        outputSection.style.cssText = `
            flex: 1;
            display: flex;
            flex-direction: column;
        `;

        const outputLabel = document.createElement('div');
        outputLabel.textContent = 'Output:';
        outputLabel.style.cssText = `
            margin-bottom: 5px;
            font-weight: bold;
        `;

        const outputTextarea = document.createElement('textarea');
        outputTextarea.style.cssText = `
            width: 100%;
            flex: 1;
            background: #1e3a5f;
            color: #ffffff;
            border: 1px solid #4a90e2;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            padding: 8px;
            resize: none;
            box-sizing: border-box;
        `;
        outputTextarea.readOnly = true;

        outputSection.appendChild(outputLabel);
        outputSection.appendChild(outputTextarea);

        // Event listeners
        processBtn.addEventListener('click', () => {
            this.processInput(inputTextarea.value, outputTextarea);
        });

        clearBtn.addEventListener('click', () => {
            inputTextarea.value = '';
            outputTextarea.value = '';
            inputTextarea.focus();
        });

        backBtn.addEventListener('click', () => {
            this.currentMode = 'menu';
            this.createInterface();
        });

        // Hover effects
        [processBtn, clearBtn, backBtn].forEach(btn => {
            btn.addEventListener('mouseover', () => {
                btn.style.opacity = '0.8';
            });
            btn.addEventListener('mouseout', () => {
                btn.style.opacity = '1';
            });
        });

        container.appendChild(header);
        container.appendChild(inputSection);
        container.appendChild(buttonSection);
        container.appendChild(outputSection);
        this.contentElement.appendChild(container);

        // Focus input
        setTimeout(() => inputTextarea.focus(), 100);
    }

    handleEscKey(e) {
        if (e.key === 'Escape') {
            if (this.currentMode !== 'menu') {
                this.currentMode = 'menu';
                this.createInterface();
            } else {
                this.close();
            }
        }
    }
    
    getInputPlaceholder() {
        const placeholders = {
            'base64encode': 'Enter text to encode (e.g., Hello World)',
            'base64decode': 'Enter Base64 string to decode (e.g., SGVsbG8gV29ybGQ=)',
            'asciitobase64': 'Enter ASCII text to convert (e.g., Test123)',
            'base64toascii': 'Enter Base64 string to convert to ASCII (e.g., VGVzdDEyMw==)'
        };
        return placeholders[this.currentMode] || '';
    }

    processInput(input, outputElement) {
        if (!input.trim()) {
            outputElement.value = 'Error: Please enter some input text.';
            return;
        }

        try {
            let result = '';

            switch (this.currentMode) {
                case 'base64encode':
                    result = this.base64EncodeText(input);
                    break;
                case 'base64decode':
                    result = this.base64DecodeText(input);
                    break;
                case 'asciitobase64':
                    result = this.asciiToBase64Text(input);
                    break;
                case 'base64toascii':
                    result = this.base64ToAsciiText(input);
                    break;
                default:
                    result = 'Error: Unknown operation mode.';
            }

            outputElement.value = result;
        } catch (error) {
            outputElement.value = `Error: ${error.message}`;
        }
    }
    
    base64EncodeText(text) {
        return btoa(text);
    }

    base64DecodeText(base64) {
        return atob(base64.trim());
    }

    asciiToBase64Text(text) {
        return btoa(text);
    }

    base64ToAsciiText(base64) {
        return atob(base64.trim());
    }
    
    close() {
        this.overlay.style.display = 'none';
        this.contentElement.innerHTML = '';
        // Remove ESC key listener
        document.removeEventListener('keydown', this.handleEscKey.bind(this));
    }
}

// Make DecoderApp globally available
window.DecoderApp = DecoderApp;
