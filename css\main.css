/* Main CSS for Windows XP File Manager */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    background: #3a6ea5;
    overflow: hidden;
    height: 100vh;
}

.window {
    width: 100vw;
    height: 100vh;
    background: #ece9d8;
    border: 2px outset #ece9d8;
    display: flex;
    flex-direction: column;
}

/* Title Bar Removed - No longer needed */

/* Menu Bar */
.menu-bar {
    height: 22px;
    background: #ece9d8;
    border-bottom: 1px solid #919b9c;
    display: flex;
    align-items: center;
    padding: 0 4px;
}

.menu-item {
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 2px;
}

.menu-item:hover {
    background: #316ac5;
    color: white;
}

/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    background: white;
    border: 1px inset #ece9d8;
    margin: 2px;
}

/* Status Bar */
.status-bar {
    height: 22px;
    background: #ece9d8;
    border-top: 1px solid #919b9c;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    font-size: 11px;
}

/* Popup Styles */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-window {
    background: #ece9d8;
    border: 2px outset #ece9d8;
    min-width: 400px;
    min-height: 300px;
    max-width: 80vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.popup-title-bar {
    height: 30px;
    background: linear-gradient(to bottom, #0997ff 0%, #0053ee 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    font-weight: bold;
}

.popup-close {
    width: 21px;
    height: 21px;
    border: 1px outset #ece9d8;
    background: #ece9d8;
    color: black;
    cursor: pointer;
    font-weight: bold;
}

.popup-close:hover {
    background: #ff6b6b;
    color: white;
}

.popup-content {
    flex: 1;
    padding: 10px;
    overflow: auto;
    background: white;
    margin: 2px;
    border: 1px inset #ece9d8;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 16px;
    height: 16px;
}

::-webkit-scrollbar-track {
    background: #ece9d8;
    border: 1px inset #ece9d8;
}

::-webkit-scrollbar-thumb {
    background: #d4d0c8;
    border: 1px outset #d4d0c8;
}

::-webkit-scrollbar-thumb:hover {
    background: #c0c0c0;
}

::-webkit-scrollbar-corner {
    background: #ece9d8;
}

/* Handle missing images gracefully */
img {
    background: #f0f0f0;
    border: 1px solid #d0d0d0;
}

img[src*="assets/icons/"]:not([src$=".png"]):not([src$=".jpg"]):not([src$=".gif"]) {
    background: #f0f0f0;
    border: 1px solid #d0d0d0;
}

/* SVG Icon Styles */
.file-icon svg,
.drive-icon svg {
    display: block;
    pointer-events: none;
}

/* Ensure SVG icons render properly */
svg {
    overflow: visible;
}

/* Placeholder for missing icons (fallback) */
.file-icon img,
.drive-icon img,
.title-icon,
.toolbar-btn img {
    background: #f0f0f0;
    border: 1px solid #d0d0d0;
    display: inline-block;
}

/* Only show emoji fallback if no SVG is present */
.file-icon:not(:has(svg)):not(:has(img))::after,
.drive-icon:not(:has(svg)):not(:has(img))::after {
    content: '📄';
    display: block;
    text-align: center;
    line-height: 1;
    font-size: 16px;
}

.drive-icon:not(:has(svg)):not(:has(img))::after {
    content: '💾';
}

/* Terminal/Decoder Styles */
.terminal-container {
    background: #012456;
    color: #ffffff;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.terminal-output {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.terminal-input-container {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.terminal-prompt {
    color: #ffffff;
    font-weight: normal;
}

.terminal-input {
    background: transparent;
    border: none;
    color: #ffffff;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    outline: none;
    flex: 1;
    margin-left: 5px;
}

.terminal-input::selection {
    background: #ffffff;
    color: #012456;
}

/* Hide broken image icon */
img[alt]:after {
    content: attr(alt);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #f0f0f0;
    padding: 2px 4px;
    font-size: 10px;
    color: #666;
    border: 1px solid #d0d0d0;
    white-space: nowrap;
}
