// Main File Manager Component for Windows XP File Manager

class FileManager {
    constructor() {
        this.currentPath = ['My Computer'];
        this.currentView = 'icon'; // 'icon', 'list', 'details'
        this.selectedItems = new Set();
        this.navigationHistory = [];
        this.navigationIndex = -1;
        
        // DOM elements
        this.fileList = document.getElementById('file-list');
        this.detailsPanel = document.getElementById('details-panel');
        this.detailsContent = document.getElementById('details-content');
        this.addressInput = document.getElementById('address-input');
        this.statusText = document.getElementById('status-text');
        this.itemCount = document.getElementById('item-count');
        this.sidebarDrives = document.getElementById('sidebar-drives');
        
        // Navigation buttons
        this.backBtn = document.getElementById('back-btn');
        this.forwardBtn = document.getElementById('forward-btn');
        
        // Initialize components
        this.fileViewer = new FileViewer();
        
        this.initializeEventListeners();
        this.populateSidebarDrives();
        this.navigate(['My Computer']);
    }
    
    initializeEventListeners() {
        // Navigation buttons
        this.backBtn.addEventListener('click', () => this.goBack());
        this.forwardBtn.addEventListener('click', () => this.goForward());
        
        // Address bar
        this.addressInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.navigateToAddress(this.addressInput.value);
            }
        });
        
        // File list events
        this.fileList.addEventListener('click', (e) => this.handleFileListClick(e));
        this.fileList.addEventListener('dblclick', (e) => this.handleFileListDoubleClick(e));
        this.fileList.addEventListener('contextmenu', (e) => this.handleContextMenu(e));
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // Window resize
        window.addEventListener('resize', () => this.updateLayout());
    }
    
    /**
     * Populate sidebar with drives
     */
    populateSidebarDrives() {
        if (!this.sidebarDrives) return;

        this.sidebarDrives.innerHTML = '';
        const drives = Object.keys(fileSystem.drives);

        drives.forEach(driveLetter => {
            const drive = fileSystem.drives[driveLetter];
            const driveElement = this.createSidebarDriveElement(driveLetter, drive);
            this.sidebarDrives.appendChild(driveElement);
        });
    }

    /**
     * Create sidebar drive element
     * @param {string} driveLetter - Drive letter (e.g., 'C:')
     * @param {Object} driveData - Drive data object
     * @returns {HTMLElement} Drive element
     */
    createSidebarDriveElement(driveLetter, driveData) {
        const element = document.createElement('div');
        element.className = 'sidebar-drive-item';
        element.dataset.drive = driveLetter;

        const icon = document.createElement('div');
        icon.className = 'sidebar-drive-icon';
        // Use emoji icons for drives
        const driveIcons = {
            'hard-drive': '💾',
            'cd-drive': '💿',
            'floppy-drive': '💾',
            'network-drive': '🌐'
        };
        icon.textContent = driveIcons[driveData.type] || '💾';

        const info = document.createElement('div');
        info.className = 'sidebar-drive-info';

        const name = document.createElement('div');
        name.className = 'sidebar-drive-name';
        name.textContent = driveLetter;

        const label = document.createElement('div');
        label.className = 'sidebar-drive-label';
        label.textContent = driveData.name;

        info.appendChild(name);
        info.appendChild(label);

        element.appendChild(icon);
        element.appendChild(info);

        // Add click event listener
        element.addEventListener('click', () => {
            this.navigate(['My Computer', driveLetter]);
            this.updateSidebarSelection(driveLetter);
        });

        return element;
    }

    /**
     * Update sidebar drive selection
     * @param {string} selectedDrive - Selected drive letter
     */
    updateSidebarSelection(selectedDrive) {
        const driveItems = this.sidebarDrives.querySelectorAll('.sidebar-drive-item');
        driveItems.forEach(item => {
            if (item.dataset.drive === selectedDrive) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }

    /**
     * Navigate to a specific path
     * @param {Array} path - Path array
     */
    navigate(path) {
        this.currentPath = [...path];
        this.updateAddressBar();
        this.updateNavigationButtons();
        this.loadCurrentDirectory();
        this.updateDetailsPanel();

        // Update sidebar selection if we're on a drive
        if (path.length >= 2 && path[0] === 'My Computer') {
            this.updateSidebarSelection(path[1]);
        } else {
            // Clear sidebar selection if we're at My Computer level
            const driveItems = this.sidebarDrives?.querySelectorAll('.sidebar-drive-item');
            driveItems?.forEach(item => item.classList.remove('selected'));
        }

        // Add to navigation history
        if (this.navigationIndex === -1 ||
            JSON.stringify(this.navigationHistory[this.navigationIndex]) !== JSON.stringify(path)) {
            this.navigationHistory = this.navigationHistory.slice(0, this.navigationIndex + 1);
            this.navigationHistory.push([...path]);
            this.navigationIndex = this.navigationHistory.length - 1;
        }

        this.updateNavigationButtons();
    }
    
    /**
     * Load and display current directory contents
     */
    loadCurrentDirectory() {
        this.clearSelection();
        
        if (this.currentPath.length === 1 && this.currentPath[0] === 'My Computer') {
            this.loadDrives();
        } else {
            this.loadDirectoryContents();
        }
    }
    
    /**
     * Load drives view
     */
    loadDrives() {
        this.fileList.innerHTML = '';
        this.fileList.className = 'file-list icon-view';
        
        const drives = Object.keys(fileSystem.drives);
        
        drives.forEach(driveLetter => {
            const drive = fileSystem.drives[driveLetter];
            const driveElement = this.createDriveElement(driveLetter, drive);
            this.fileList.appendChild(driveElement);
        });
        
        this.updateStatusBar(drives.length, 'drives');
    }
    
    /**
     * Create drive element
     * @param {string} driveLetter - Drive letter (e.g., 'C:')
     * @param {Object} driveData - Drive data object
     * @returns {HTMLElement} Drive element
     */
    createDriveElement(driveLetter, driveData) {
        const element = document.createElement('div');
        element.className = 'drive-item file-item';
        element.dataset.name = driveLetter;
        element.dataset.type = 'drive';

        // Create icon container
        const iconContainer = document.createElement('div');
        iconContainer.className = 'drive-icon';

        const iconData = fileSystem.getDriveIcon(driveData.type);
        if (iconData.startsWith('<svg')) {
            // SVG icon
            iconContainer.innerHTML = iconData;
        } else {
            // Fallback to IMG element for PNG
            const icon = document.createElement('img');
            icon.src = iconData;
            icon.alt = driveLetter;
            iconContainer.appendChild(icon);
        }

        const name = document.createElement('div');
        name.className = 'drive-name';
        name.textContent = driveLetter;

        const label = document.createElement('div');
        label.className = 'drive-label';
        label.textContent = driveData.name;

        element.appendChild(iconContainer);
        element.appendChild(name);
        element.appendChild(label);

        return element;
    }
    
    /**
     * Load directory contents
     */
    loadDirectoryContents() {
        const contents = this.getCurrentDirectoryContents();
        
        if (!contents) {
            this.showError('Directory not found');
            return;
        }
        
        this.fileList.innerHTML = '';
        this.fileList.className = `file-list ${this.currentView}-view`;
        
        // Add header for details view
        if (this.currentView === 'details') {
            const header = this.createDetailsHeader();
            this.fileList.appendChild(header);
        }
        
        const items = Object.keys(contents).map(name => ({
            name,
            ...contents[name]
        }));
        
        // Sort items
        const sortedItems = FileUtils.sortItems(items, 'name', true);
        
        sortedItems.forEach(item => {
            const element = this.createFileElement(item);
            this.fileList.appendChild(element);
        });
        
        this.updateStatusBar(items.length, 'items');
    }
    
    /**
     * Get current directory contents
     * @returns {Object|null} Directory contents or null if not found
     */
    getCurrentDirectoryContents() {
        if (this.currentPath.length === 1 && this.currentPath[0] === 'My Computer') {
            return fileSystem.drives;
        }
        
        let current = fileSystem.drives;
        
        for (let i = 1; i < this.currentPath.length; i++) {
            const pathPart = this.currentPath[i];
            
            if (i === 1) {
                // Drive level
                current = current[pathPart];
                if (!current) return null;
                current = current.contents;
            } else {
                // Folder level
                current = current[pathPart];
                if (!current || current.type !== 'folder') return null;
                current = current.contents;
            }
            
            if (!current) return null;
        }
        
        return current;
    }
    
    /**
     * Create details header for details view
     * @returns {HTMLElement} Header element
     */
    createDetailsHeader() {
        const header = document.createElement('div');
        header.className = 'file-header';
        
        header.innerHTML = `
            <div class="header-name">Name</div>
            <div class="header-size">Size</div>
            <div class="header-type">Type</div>
            <div class="header-date">Date Modified</div>
        `;
        
        return header;
    }
    
    /**
     * Create file element
     * @param {Object} item - File or folder item
     * @returns {HTMLElement} File element
     */
    createFileElement(item) {
        const element = document.createElement('div');
        element.className = 'file-item';
        element.dataset.name = item.name;
        element.dataset.type = item.type;

        // Create icon container
        const iconContainer = document.createElement('div');
        iconContainer.className = 'file-icon';

        const iconData = fileSystem.getFileIcon(item);
        if (iconData.startsWith('<svg')) {
            // SVG icon
            iconContainer.innerHTML = iconData;
        } else {
            // Fallback to IMG element for PNG
            const icon = document.createElement('img');
            icon.src = iconData;
            icon.alt = item.name;
            iconContainer.appendChild(icon);
        }

        const name = document.createElement('div');
        name.className = 'file-name';
        name.textContent = item.name;
        name.title = item.name;

        element.appendChild(iconContainer);
        element.appendChild(name);
        
        // Add additional info for list and details view
        if (this.currentView === 'list' || this.currentView === 'details') {
            if (item.type !== 'folder') {
                const size = document.createElement('div');
                size.className = 'file-size';
                size.textContent = fileSystem.formatFileSize(item.size || 0);
                element.appendChild(size);
            } else {
                const size = document.createElement('div');
                size.className = 'file-size';
                size.textContent = '';
                element.appendChild(size);
            }
            
            if (this.currentView === 'details') {
                const type = document.createElement('div');
                type.className = 'file-type';
                type.textContent = item.type === 'folder' ? 'File folder' : 
                    FileUtils.getFileTypeDescription(item.extension);
                element.appendChild(type);
            }
            
            const date = document.createElement('div');
            date.className = 'file-date';
            date.textContent = fileSystem.formatDate(item.modified || item.created || new Date());
            element.appendChild(date);
        }
        
        return element;
    }
    
    /**
     * Update address bar
     */
    updateAddressBar() {
        if (this.currentPath.length === 1 && this.currentPath[0] === 'My Computer') {
            this.addressInput.value = 'My Computer';
        } else {
            this.addressInput.value = this.currentPath.join('\\');
        }
    }
    
    /**
     * Update navigation buttons state
     */
    updateNavigationButtons() {
        this.backBtn.disabled = this.navigationIndex <= 0;
        this.forwardBtn.disabled = this.navigationIndex >= this.navigationHistory.length - 1;
    }
    
    /**
     * Update status bar
     * @param {number} count - Number of items
     * @param {string} type - Type of items
     */
    updateStatusBar(count, type) {
        this.statusText.textContent = 'Ready';
        this.itemCount.textContent = `${count} ${type}`;
    }

    /**
     * Handle file list click
     * @param {Event} e - Click event
     */
    handleFileListClick(e) {
        const fileItem = e.target.closest('.file-item');
        if (!fileItem) {
            this.clearSelection();
            return;
        }

        if (e.ctrlKey) {
            this.toggleSelection(fileItem);
        } else {
            this.selectItem(fileItem);
        }

        this.updateDetailsPanel();
    }

    /**
     * Handle file list double click
     * @param {Event} e - Double click event
     */
    handleFileListDoubleClick(e) {
        const fileItem = e.target.closest('.file-item');
        if (!fileItem) return;

        const name = fileItem.dataset.name;
        const type = fileItem.dataset.type;

        if (type === 'drive') {
            this.navigate([...this.currentPath, name]);
        } else if (type === 'folder') {
            this.navigate([...this.currentPath, name]);
        } else {
            // Open file in viewer
            const fileData = this.getFileData(name);
            if (fileData) {
                this.fileViewer.openFile(name, fileData);
            }
        }
    }

    /**
     * Handle context menu
     * @param {Event} e - Context menu event
     */
    handleContextMenu(e) {
        e.preventDefault();
        // Context menu implementation would go here
        console.log('Context menu requested');
    }

    /**
     * Handle keyboard navigation
     * @param {Event} e - Keydown event
     */
    handleKeydown(e) {
        if (e.target.tagName === 'INPUT') return;

        switch (e.key) {
            case 'Enter':
                const selected = this.fileList.querySelector('.file-item.selected');
                if (selected) {
                    this.handleFileListDoubleClick({ target: selected });
                }
                break;
            case 'Backspace':
                if (this.currentPath.length > 1) {
                    this.goUp();
                }
                break;
            case 'F5':
                e.preventDefault();
                this.refresh();
                break;
        }
    }

    /**
     * Go back in navigation history
     */
    goBack() {
        if (this.navigationIndex > 0) {
            this.navigationIndex--;
            const path = this.navigationHistory[this.navigationIndex];
            this.currentPath = [...path];
            this.updateAddressBar();
            this.loadCurrentDirectory();
            this.updateNavigationButtons();
            this.updateDetailsPanel();
        }
    }

    /**
     * Go forward in navigation history
     */
    goForward() {
        if (this.navigationIndex < this.navigationHistory.length - 1) {
            this.navigationIndex++;
            const path = this.navigationHistory[this.navigationIndex];
            this.currentPath = [...path];
            this.updateAddressBar();
            this.loadCurrentDirectory();
            this.updateNavigationButtons();
            this.updateDetailsPanel();
        }
    }

    /**
     * Go up one level
     */
    goUp() {
        if (this.currentPath.length > 1) {
            const newPath = this.currentPath.slice(0, -1);
            this.navigate(newPath);
        }
    }



    /**
     * Set view mode
     * @param {string} view - View mode ('icon', 'list', 'details')
     */
    setView(view) {
        this.currentView = view;
        this.loadCurrentDirectory();
    }

    /**
     * Select an item
     * @param {HTMLElement} item - Item element
     */
    selectItem(item) {
        this.clearSelection();
        item.classList.add('selected');
        this.selectedItems.add(item.dataset.name);
    }

    /**
     * Toggle item selection
     * @param {HTMLElement} item - Item element
     */
    toggleSelection(item) {
        if (item.classList.contains('selected')) {
            item.classList.remove('selected');
            this.selectedItems.delete(item.dataset.name);
        } else {
            item.classList.add('selected');
            this.selectedItems.add(item.dataset.name);
        }
    }

    /**
     * Clear all selections
     */
    clearSelection() {
        this.fileList.querySelectorAll('.file-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        this.selectedItems.clear();
    }

    /**
     * Get file data for selected item
     * @param {string} name - File name
     * @returns {Object|null} File data
     */
    getFileData(name) {
        const contents = this.getCurrentDirectoryContents();
        return contents ? contents[name] : null;
    }

    /**
     * Update details panel
     */
    updateDetailsPanel() {
        if (this.selectedItems.size === 0) {
            this.showEmptyDetails();
        } else if (this.selectedItems.size === 1) {
            const name = Array.from(this.selectedItems)[0];
            this.showItemDetails(name);
        } else {
            this.showMultipleItemsDetails();
        }
    }

    /**
     * Show empty details panel
     */
    showEmptyDetails() {
        this.detailsContent.innerHTML = `
            <div class="details-empty">
                <img src="assets/icons/files/unknown.png" alt="" class="details-empty-icon">
                <p>Select a file or folder to view its details.</p>
            </div>
        `;
    }

    /**
     * Show details for single item
     * @param {string} name - Item name
     */
    showItemDetails(name) {
        const item = this.getFileData(name);
        if (!item) return;

        if (item.type === 'folder') {
            this.showFolderDetails(name, item);
        } else {
            this.showFileDetails(name, item);
        }
    }

    /**
     * Show file details
     * @param {string} name - File name
     * @param {Object} fileData - File data
     */
    showFileDetails(name, fileData) {
        const iconData = fileSystem.getFileIcon(fileData);
        let iconHtml;

        if (iconData.startsWith('<svg')) {
            iconHtml = `<div class="file-preview-icon">${iconData}</div>`;
        } else {
            iconHtml = `<img src="${iconData}" alt="${name}" class="file-preview-icon">`;
        }

        this.detailsContent.innerHTML = `
            <div class="file-details">
                <div class="file-preview">
                    ${iconHtml}
                    <div class="file-preview-name">${name}</div>
                </div>
                <div class="file-properties">
                    <h4>Properties</h4>
                    <div class="property-row">
                        <div class="property-label">Type:</div>
                        <div class="property-value">${FileUtils.getFileTypeDescription(fileData.extension)}</div>
                    </div>
                    <div class="property-row">
                        <div class="property-label">Size:</div>
                        <div class="property-value">${fileSystem.formatFileSize(fileData.size || 0)}</div>
                    </div>
                    <div class="property-row">
                        <div class="property-label">Created:</div>
                        <div class="property-value">${fileSystem.formatDate(fileData.created)}</div>
                    </div>
                    <div class="property-row">
                        <div class="property-label">Modified:</div>
                        <div class="property-value">${fileSystem.formatDate(fileData.modified)}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Show folder details
     * @param {string} name - Folder name
     * @param {Object} folderData - Folder data
     */
    showFolderDetails(name, folderData) {
        const itemCount = folderData.contents ? Object.keys(folderData.contents).length : 0;
        const iconData = fileSystem.getFileIcon(folderData);
        let iconHtml;

        if (iconData.startsWith('<svg')) {
            iconHtml = `<div class="folder-preview-icon">${iconData}</div>`;
        } else {
            iconHtml = `<img src="${iconData}" alt="${name}" class="folder-preview-icon">`;
        }

        this.detailsContent.innerHTML = `
            <div class="folder-details">
                <div class="folder-preview">
                    ${iconHtml}
                    <div class="folder-preview-name">${name}</div>
                </div>
                <div class="folder-stats">
                    <h4>Contents</h4>
                    <div class="stat-row">
                        <div class="stat-label">Items:</div>
                        <div class="stat-value">${itemCount}</div>
                    </div>
                    <div class="stat-row">
                        <div class="stat-label">Created:</div>
                        <div class="stat-value">${fileSystem.formatDate(folderData.created)}</div>
                    </div>
                    <div class="stat-row">
                        <div class="stat-label">Modified:</div>
                        <div class="stat-value">${fileSystem.formatDate(folderData.modified)}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Show details for multiple selected items
     */
    showMultipleItemsDetails() {
        this.detailsContent.innerHTML = `
            <div class="details-empty">
                <p>${this.selectedItems.size} items selected</p>
            </div>
        `;
    }

    /**
     * Refresh current directory
     */
    refresh() {
        this.loadCurrentDirectory();
    }

    /**
     * Navigate to address
     * @param {string} address - Address string
     */
    navigateToAddress(address) {
        // Simple address parsing - in real implementation would be more robust
        if (address === 'My Computer') {
            this.navigate(['My Computer']);
        } else {
            const parts = address.split('\\').filter(part => part.length > 0);
            if (parts.length > 0) {
                this.navigate(['My Computer', ...parts]);
            }
        }
    }

    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        this.fileList.innerHTML = `
            <div class="file-list-empty">
                <img src="assets/icons/files/unknown.png" alt="" class="file-list-empty-icon">
                <p>Error: ${message}</p>
            </div>
        `;
    }

    /**
     * Update layout for responsive design
     */
    updateLayout() {
        // Layout updates for responsive design would go here
    }
}

// Make FileManager globally available
window.FileManager = FileManager;
