// SVG Icon System for Windows XP File Manager

const SvgIcons = {
    // Drive Icons
    drives: {
        'hard-drive': `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <rect x="4" y="8" width="24" height="16" rx="2" fill="#e0e0e0" stroke="#666" stroke-width="1"/>
                <rect x="6" y="10" width="20" height="3" fill="#4a90e2"/>
                <rect x="26" y="14" width="4" height="4" fill="#333"/>
                <circle cx="7" cy="21" r="1" fill="#666"/>
                <circle cx="25" cy="21" r="1" fill="#666"/>
            </svg>`,
        
        'cd-drive': `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="6" width="28" height="20" rx="2" fill="#f0f0f0" stroke="#666" stroke-width="1"/>
                <circle cx="16" cy="16" r="8" fill="#e0e0e0" stroke="#999" stroke-width="1"/>
                <circle cx="16" cy="16" r="2" fill="#666"/>
                <rect x="4" y="15" width="24" height="2" fill="#333"/>
            </svg>`,
        
        'floppy-drive': `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <rect x="6" y="4" width="20" height="24" rx="1" fill="#2c5aa0" stroke="#000" stroke-width="1"/>
                <rect x="8" y="6" width="16" height="8" fill="#c0c0c0" stroke="#999" stroke-width="1"/>
                <rect x="8" y="16" width="16" height="8" fill="#ffffff" stroke="#ccc" stroke-width="1"/>
                <rect x="24" y="8" width="2" height="4" fill="#000"/>
                <circle cx="16" cy="10" r="2" fill="#666"/>
            </svg>`,
        
        'network-drive': `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <rect x="4" y="8" width="24" height="16" rx="2" fill="#4a90e2" stroke="#1e3f73" stroke-width="1"/>
                <circle cx="12" cy="16" r="3" fill="none" stroke="#fff" stroke-width="2"/>
                <circle cx="20" cy="16" r="3" fill="none" stroke="#fff" stroke-width="2"/>
                <line x1="15" y1="16" x2="17" y2="16" stroke="#fff" stroke-width="2"/>
                <circle cx="28" cy="12" r="2" fill="#00ff00"/>
            </svg>`
    },

    // File Type Icons
    files: {
        'folder': `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 8 L14 8 L16 6 L28 6 L28 26 L4 26 Z" fill="#ffd700" stroke="#b8860b" stroke-width="1"/>
                <path d="M4 10 L28 10 L28 26 L4 26 Z" fill="#ffed4e" stroke="#b8860b" stroke-width="1"/>
            </svg>`,
        
        'document': `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="#ffffff" stroke="#999" stroke-width="1"/>
                <path d="M20 4 L20 10 L26 10" fill="#d0d0d0" stroke="#999" stroke-width="1"/>
                <line x1="9" y1="14" x2="23" y2="14" stroke="#333" stroke-width="1"/>
                <line x1="9" y1="17" x2="23" y2="17" stroke="#333" stroke-width="1"/>
                <line x1="9" y1="20" x2="18" y2="20" stroke="#333" stroke-width="1"/>
            </svg>`,
        
        'spreadsheet': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="sheet-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#4caf50"/>
                        <stop offset="100%" style="stop-color:#2e7d32"/>
                    </linearGradient>
                </defs>
                <!-- Spreadsheet body -->
                <rect x="4" y="4" width="24" height="24" fill="url(#sheet-gradient)" stroke="#1b5e20" stroke-width="1"/>
                <!-- Grid lines -->
                <line x1="4" y1="12" x2="28" y2="12" stroke="#fff" stroke-width="1"/>
                <line x1="4" y1="20" x2="28" y2="20" stroke="#fff" stroke-width="1"/>
                <line x1="12" y1="4" x2="12" y2="28" stroke="#fff" stroke-width="1"/>
                <line x1="20" y1="4" x2="20" y2="28" stroke="#fff" stroke-width="1"/>
                <!-- Chart icon -->
                <rect x="14" y="14" width="2" height="4" fill="#fff"/>
                <rect x="17" y="16" width="2" height="2" fill="#fff"/>
                <rect x="20" y="12" width="2" height="6" fill="#fff"/>
            </svg>
        `,
        
        'pdf': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="pdf-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff5722"/>
                        <stop offset="100%" style="stop-color:#d32f2f"/>
                    </linearGradient>
                </defs>
                <!-- PDF body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#pdf-gradient)" stroke="#b71c1c" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#d32f2f" stroke="#b71c1c" stroke-width="1"/>
                <!-- PDF text -->
                <text x="16" y="20" text-anchor="middle" fill="#fff" font-family="Arial" font-size="8" font-weight="bold">PDF</text>
            </svg>
        `,
        
        'image': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="img-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e1f5fe"/>
                        <stop offset="100%" style="stop-color:#81d4fa"/>
                    </linearGradient>
                </defs>
                <!-- Image frame -->
                <rect x="4" y="4" width="24" height="24" fill="url(#img-gradient)" stroke="#0277bd" stroke-width="1"/>
                <!-- Mountain -->
                <path d="M8 20 L12 14 L16 18 L20 12 L24 20 Z" fill="#4caf50"/>
                <!-- Sun -->
                <circle cx="22" cy="10" r="3" fill="#ffeb3b"/>
            </svg>
        `,

        'audio': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="audio-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#9c27b0"/>
                        <stop offset="100%" style="stop-color:#6a1b9a"/>
                    </linearGradient>
                </defs>
                <!-- Audio file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#audio-gradient)" stroke="#4a148c" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#7b1fa2" stroke="#4a148c" stroke-width="1"/>
                <!-- Musical note -->
                <circle cx="14" cy="20" r="2" fill="#fff"/>
                <rect x="16" y="14" width="1" height="6" fill="#fff"/>
                <path d="M17 14 Q19 13 19 15 L19 17 Q17 18 17 16 Z" fill="#fff"/>
            </svg>
        `,

        'video': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="video-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f44336"/>
                        <stop offset="100%" style="stop-color:#c62828"/>
                    </linearGradient>
                </defs>
                <!-- Video file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#video-gradient)" stroke="#b71c1c" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#d32f2f" stroke="#b71c1c" stroke-width="1"/>
                <!-- Play button -->
                <circle cx="16" cy="18" r="5" fill="#fff"/>
                <path d="M14 15 L14 21 L20 18 Z" fill="#f44336"/>
            </svg>
        `,

        'executable': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="exe-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#607d8b"/>
                        <stop offset="100%" style="stop-color:#37474f"/>
                    </linearGradient>
                </defs>
                <!-- Executable body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#exe-gradient)" stroke="#263238" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#455a64" stroke="#263238" stroke-width="1"/>
                <!-- Gear icon -->
                <circle cx="16" cy="18" r="4" fill="none" stroke="#fff" stroke-width="1"/>
                <circle cx="16" cy="18" r="2" fill="#fff"/>
                <rect x="15" y="12" width="2" height="2" fill="#fff"/>
                <rect x="15" y="22" width="2" height="2" fill="#fff"/>
                <rect x="22" y="17" width="2" height="2" fill="#fff"/>
                <rect x="10" y="17" width="2" height="2" fill="#fff"/>
            </svg>
        `,

        'archive': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="archive-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff9800"/>
                        <stop offset="100%" style="stop-color:#e65100"/>
                    </linearGradient>
                </defs>
                <!-- Archive body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#archive-gradient)" stroke="#bf360c" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#f57c00" stroke="#bf360c" stroke-width="1"/>
                <!-- Zipper -->
                <rect x="14" y="12" width="4" height="12" fill="#fff"/>
                <rect x="15" y="13" width="1" height="1" fill="#333"/>
                <rect x="15" y="15" width="1" height="1" fill="#333"/>
                <rect x="15" y="17" width="1" height="1" fill="#333"/>
                <rect x="15" y="19" width="1" height="1" fill="#333"/>
                <rect x="15" y="21" width="1" height="1" fill="#333"/>
            </svg>
        `,

        'text': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="text-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffffff"/>
                        <stop offset="100%" style="stop-color:#e0e0e0"/>
                    </linearGradient>
                </defs>
                <!-- Text file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#text-gradient)" stroke="#999" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#d0d0d0" stroke="#999" stroke-width="1"/>
                <!-- Text lines -->
                <line x1="9" y1="13" x2="23" y2="13" stroke="#333" stroke-width="1"/>
                <line x1="9" y1="16" x2="23" y2="16" stroke="#333" stroke-width="1"/>
                <line x1="9" y1="19" x2="20" y2="19" stroke="#333" stroke-width="1"/>
                <line x1="9" y1="22" x2="23" y2="22" stroke="#333" stroke-width="1"/>
                <line x1="9" y1="25" x2="18" y2="25" stroke="#333" stroke-width="1"/>
            </svg>
        `,

        'web': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="web-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#2196f3"/>
                        <stop offset="100%" style="stop-color:#0d47a1"/>
                    </linearGradient>
                </defs>
                <!-- Web file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#web-gradient)" stroke="#01579b" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#1976d2" stroke="#01579b" stroke-width="1"/>
                <!-- Globe icon -->
                <circle cx="16" cy="18" r="5" fill="none" stroke="#fff" stroke-width="1"/>
                <path d="M11 18 Q16 14 21 18 Q16 22 11 18" fill="none" stroke="#fff" stroke-width="1"/>
                <line x1="16" y1="13" x2="16" y2="23" stroke="#fff" stroke-width="1"/>
            </svg>
        `,

        'system': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="sys-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#795548"/>
                        <stop offset="100%" style="stop-color:#3e2723"/>
                    </linearGradient>
                </defs>
                <!-- System file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#sys-gradient)" stroke="#1b0000" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#5d4037" stroke="#1b0000" stroke-width="1"/>
                <!-- Warning triangle -->
                <path d="M16 14 L20 22 L12 22 Z" fill="#ffeb3b" stroke="#f57f17" stroke-width="1"/>
                <text x="16" y="20" text-anchor="middle" fill="#333" font-family="Arial" font-size="8" font-weight="bold">!</text>
            </svg>
        `,

        'shortcut': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="shortcut-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffffff"/>
                        <stop offset="100%" style="stop-color:#e0e0e0"/>
                    </linearGradient>
                </defs>
                <!-- Shortcut file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#shortcut-gradient)" stroke="#999" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#d0d0d0" stroke="#999" stroke-width="1"/>
                <!-- Arrow -->
                <path d="M10 16 L18 16 L15 13 M18 16 L15 19" fill="none" stroke="#0066cc" stroke-width="2"/>
                <!-- Link symbol -->
                <circle cx="22" cy="22" r="3" fill="#0066cc"/>
                <path d="M20 20 L24 24 M24 20 L20 24" stroke="#fff" stroke-width="1"/>
            </svg>
        `,

        'unknown': `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="unknown-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#9e9e9e"/>
                        <stop offset="100%" style="stop-color:#616161"/>
                    </linearGradient>
                </defs>
                <!-- Unknown file body -->
                <path d="M6 4 L20 4 L26 10 L26 28 L6 28 Z" fill="url(#unknown-gradient)" stroke="#424242" stroke-width="1"/>
                <!-- Folded corner -->
                <path d="M20 4 L20 10 L26 10" fill="#757575" stroke="#424242" stroke-width="1"/>
                <!-- Question mark -->
                <text x="16" y="22" text-anchor="middle" fill="#fff" font-family="Arial" font-size="12" font-weight="bold">?</text>
            </svg>
        `
    },

    /**
     * Get SVG icon for drive type
     * @param {string} driveType - Type of drive
     * @returns {string} SVG markup
     */
    getDriveIcon(driveType) {
        return this.drives[driveType] || this.drives['hard-drive'];
    },

    /**
     * Get SVG icon for file type
     * @param {string} fileType - Type of file
     * @returns {string} SVG markup
     */
    getFileIcon(fileType) {
        return this.files[fileType] || this.files['document'];
    },

    /**
     * Create SVG element from markup
     * @param {string} svgMarkup - SVG markup string
     * @returns {Element} SVG DOM element
     */
    createSvgElement(svgMarkup) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(svgMarkup.trim(), 'image/svg+xml');
        return doc.documentElement;
    }
};

// Make SvgIcons globally available
window.SvgIcons = SvgIcons;
