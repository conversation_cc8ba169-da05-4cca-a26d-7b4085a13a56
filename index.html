<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Computer - Windows XP</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/xp-theme.css">
    <link rel="stylesheet" href="css/components/toolbar.css">
    <link rel="stylesheet" href="css/components/sidebar.css">
    <link rel="stylesheet" href="css/components/file-list.css">
    <link rel="stylesheet" href="css/components/details-panel.css">
</head>
<body>
    <div class="window">
        <!-- Menu Bar -->
        <div class="menu-bar">
            <div class="menu-item">File</div>
            <div class="menu-item">Edit</div>
            <div class="menu-item">View</div>
            <div class="menu-item">Tools</div>
            <div class="menu-item">Help</div>
        </div>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="address-bar">
                <button class="nav-btn" id="back-btn" title="Back">
                    <svg width="30" height="20" viewBox="0 0 30 20" xmlns="http://www.w3.org/2000/svg">
                        <rect width="30" height="20" rx="3" ry="3" fill="#28a745" />
                        <polygon points="8,10 15,5 15,8 22,8 22,12 15,12 15,15" fill="white"/>
                    </svg>
                </button>
                <button class="nav-btn" id="forward-btn" title="Forward">
                    <svg width="30" height="20" viewBox="0 0 30 20" xmlns="http://www.w3.org/2000/svg">
                        <rect width="30" height="20" rx="3" ry="3" fill="#28a745" />
                        <polygon points="22,10 15,5 15,8 8,8 8,12 15,12 15,15" fill="white"/>
                    </svg>
                </button>
                <label>Address:</label>
                <input type="text" id="address-input" value="My Computer" readonly>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="content-area">
            <!-- Left Sidebar -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>Drives</h3>
                    <div class="drives-list" id="sidebar-drives">
                        <!-- Drives will be populated here -->
                    </div>
                </div>
            </div>

            <!-- File List Area -->
            <div class="file-area">
                <div class="file-list" id="file-list">
                    <!-- Files and folders will be populated here -->
                </div>
            </div>

            <!-- Details Panel -->
            <div class="details-panel" id="details-panel">
                <div class="details-header">Details</div>
                <div class="details-content" id="details-content">
                    <p>Select a file or folder to view its details.</p>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="status-text">Ready</span>
            <span id="item-count">0 objects</span>
        </div>
    </div>

    <!-- File Viewer Popup -->
    <div class="popup-overlay" id="popup-overlay" style="display: none;">
        <div class="popup-window">
            <div class="popup-title-bar">
                <span class="popup-title" id="popup-title">File Viewer</span>
                <button class="popup-close" id="popup-close">×</button>
            </div>
            <div class="popup-content" id="popup-content">
                <!-- File content will be displayed here -->
            </div>
        </div>
    </div>

    <script src="js/svgIcons.js"></script>
    <script src="data/fileSystem.js"></script>
    <script src="js/utils/dateUtils.js"></script>
    <script src="js/utils/fileUtils.js"></script>
    <script src="js/fileViewer.js"></script>
    <script src="js/decoder.js"></script>
    <script src="js/fileManager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
