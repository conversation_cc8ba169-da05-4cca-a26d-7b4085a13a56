/* File List Component Styles */

.file-area {
    flex: 1;
    background: white;
    border: 2px inset #ece9d8;
    margin: 2px;
    overflow: auto;
    position: relative;
}

.file-list {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    align-content: start;
}

/* Icon View (Default) */
.file-list.icon-view {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
}

.file-list.icon-view .file-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px;
    cursor: pointer;
    border-radius: 2px;
    text-align: center;
    min-height: 80px;
    justify-content: flex-start;
}

.file-list.icon-view .file-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 4px;
}

.file-list.icon-view .file-name {
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    word-wrap: break-word;
    max-width: 70px;
    line-height: 1.2;
}

/* List View */
.file-list.list-view {
    display: block;
    padding: 0;
}

.file-list.list-view .file-item {
    display: flex;
    align-items: center;
    padding: 2px 8px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    height: 20px;
}

.file-list.list-view .file-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    flex-shrink: 0;
}

.file-list.list-view .file-name {
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-list.list-view .file-size {
    width: 80px;
    text-align: right;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #666;
    margin-right: 12px;
}

.file-list.list-view .file-date {
    width: 120px;
    text-align: right;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #666;
}

/* Details View */
.file-list.details-view {
    display: block;
    padding: 0;
}

.file-list.details-view .file-header {
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #919b9c;
    padding: 4px 8px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.file-list.details-view .file-header .header-name {
    flex: 1;
    margin-left: 22px;
}

.file-list.details-view .file-header .header-size {
    width: 80px;
    text-align: right;
    margin-right: 12px;
}

.file-list.details-view .file-header .header-type {
    width: 100px;
    text-align: left;
    margin-right: 12px;
}

.file-list.details-view .file-header .header-date {
    width: 120px;
    text-align: right;
}

.file-list.details-view .file-item {
    display: flex;
    align-items: center;
    padding: 2px 8px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    height: 20px;
}

.file-list.details-view .file-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    flex-shrink: 0;
}

.file-list.details-view .file-name {
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-list.details-view .file-size {
    width: 80px;
    text-align: right;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #666;
    margin-right: 12px;
}

.file-list.details-view .file-type {
    width: 100px;
    text-align: left;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #666;
    margin-right: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-list.details-view .file-date {
    width: 120px;
    text-align: right;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #666;
}

/* File Item States */
.file-item:hover {
    background: #e0e0e0;
}

.file-item.selected {
    background: #316ac5;
    color: white;
}

.file-item.selected .file-size,
.file-item.selected .file-type,
.file-item.selected .file-date {
    color: #ccc;
}

.file-item:active {
    background: #0078d4;
}

/* Drive Items */
.drive-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    border-radius: 2px;
    text-align: center;
    min-height: 100px;
    justify-content: flex-start;
}

.drive-item .drive-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 6px;
}

.drive-item .drive-name {
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 2px;
}

.drive-item .drive-label {
    font-family: 'Tahoma', sans-serif;
    font-size: 10px;
    color: #666;
    word-wrap: break-word;
    max-width: 80px;
}

/* Empty State */
.file-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
}

.file-list-empty-icon {
    width: 48px;
    height: 48px;
    opacity: 0.5;
    margin-bottom: 12px;
}

/* Loading State */
.file-list-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    color: #666;
}

/* Selection Rectangle */
.selection-rectangle {
    position: absolute;
    border: 1px dashed #316ac5;
    background: rgba(49, 106, 197, 0.1);
    pointer-events: none;
    z-index: 100;
}

/* Context Menu */
.file-context-menu {
    position: absolute;
    background: #f0f0f0;
    border: 2px outset #f0f0f0;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
    min-width: 150px;
    z-index: 1000;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
}

.file-context-menu-item {
    padding: 4px 20px 4px 8px;
    cursor: pointer;
    border-bottom: 1px solid #d0d0d0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-context-menu-item:last-child {
    border-bottom: none;
}

.file-context-menu-item:hover {
    background: #316ac5;
    color: white;
}

.file-context-menu-separator {
    height: 1px;
    background: #d0d0d0;
    margin: 2px 0;
}
