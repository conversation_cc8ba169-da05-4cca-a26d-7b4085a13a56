/* Sidebar Component Styles */

.sidebar {
    width: 200px;
    background: #ece9d8;
    border-right: 1px solid #919b9c;
    padding: 8px;
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebar-section {
    margin-bottom: 16px;
    background: #ece9d8;
    border: 1px solid #919b9c;
    border-radius: 4px;
}

.sidebar-section h3 {
    background: linear-gradient(to bottom, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 100%);
    border-bottom: 1px solid #919b9c;
    padding: 6px 8px;
    margin: 0;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    font-weight: bold;
    color: #000;
    border-radius: 3px 3px 0 0;
}

.sidebar-section ul {
    list-style: none;
    padding: 8px;
    margin: 0;
}

.sidebar-section li {
    margin-bottom: 4px;
}

.sidebar-section a {
    color: #0000ee;
    text-decoration: none;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    display: block;
    padding: 2px 4px;
    border-radius: 2px;
    cursor: pointer;
}

.sidebar-section a:hover {
    color: #ff0000;
    background: #e0e0e0;
    text-decoration: underline;
}

.sidebar-section a:visited {
    color: #551a8b;
}

.sidebar-section a:active {
    background: #316ac5;
    color: white;
}

/* Drives List in Sidebar */
.drives-list {
    padding: 4px;
}

.sidebar-drive-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    cursor: pointer;
    border-radius: 3px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    margin-bottom: 2px;
    border: 1px solid transparent;
}

.sidebar-drive-item:hover {
    background: #e0e0e0;
    border: 1px solid #c0c0c0;
}

.sidebar-drive-item.selected {
    background: #316ac5;
    color: white;
    border: 1px solid #1e4a7a;
}

.sidebar-drive-icon {
    width: 20px;
    height: 20px;
    background: #f0f0f0;
    border: 1px solid #d0d0d0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.sidebar-drive-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.sidebar-drive-name {
    font-weight: bold;
    line-height: 1.2;
}

.sidebar-drive-label {
    font-size: 10px;
    color: #666;
    line-height: 1.1;
}

.sidebar-drive-item.selected .sidebar-drive-label {
    color: #ccc;
}

/* Legacy drive item styles for backward compatibility */
.drive-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px;
    cursor: pointer;
    border-radius: 2px;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
}

.drive-item:hover {
    background: #e0e0e0;
}

.drive-item.selected {
    background: #316ac5;
    color: white;
}

.drive-icon {
    width: 16px;
    height: 16px;
}

.drive-label {
    flex: 1;
}

.drive-info {
    font-size: 10px;
    color: #666;
}

.drive-item.selected .drive-info {
    color: #ccc;
}

/* Collapsible Sections */
.sidebar-section.collapsed h3::before {
    content: '▶ ';
    font-size: 8px;
    margin-right: 4px;
}

.sidebar-section.expanded h3::before {
    content: '▼ ';
    font-size: 8px;
    margin-right: 4px;
}

.sidebar-section.collapsed ul {
    display: none;
}

.sidebar-section h3 {
    cursor: pointer;
    user-select: none;
}

/* Special Task Links */
.task-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 3px 4px;
    text-decoration: none;
    color: #0000ee;
    font-family: 'Tahoma', sans-serif;
    font-size: 11px;
    border-radius: 2px;
}

.task-link:hover {
    background: #e0e0e0;
    color: #ff0000;
    text-decoration: underline;
}

.task-icon {
    width: 16px;
    height: 16px;
}

/* Sidebar Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 16px;
}

.sidebar::-webkit-scrollbar-track {
    background: #ece9d8;
    border: 1px inset #ece9d8;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #d4d0c8;
    border: 1px outset #d4d0c8;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #c0c0c0;
}

/* Responsive Sidebar */
@media (max-width: 768px) {
    .sidebar {
        width: 180px;
    }
    
    .sidebar-section {
        margin-bottom: 12px;
    }
    
    .sidebar-section ul {
        padding: 6px;
    }
}

@media (max-width: 600px) {
    .sidebar {
        display: none;
    }
    
    .sidebar.mobile-visible {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 100;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.3);
    }
}

/* Sidebar Toggle Button (for mobile) */
.sidebar-toggle {
    display: none;
    width: 32px;
    height: 32px;
    border: 1px solid transparent;
    background: transparent;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.sidebar-toggle:hover {
    border: 1px solid #316ac5;
    background: linear-gradient(to bottom, #e3f2fd 0%, #bbdefb 100%);
}

@media (max-width: 600px) {
    .sidebar-toggle {
        display: flex;
    }
}
